{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-pl-rPL/values-pl-rPL.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-pl-rPL\\values-pl-rPL.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,199,261,344,409,484,554,628,702,770,830", "endColumns": "77,65,61,82,64,74,69,73,73,67,59,70", "endOffsets": "128,194,256,339,404,479,549,623,697,765,825,896"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1639,2435,2584,2646,2729,3015,3565,3769,4011,4335,4691,4979", "endColumns": "77,65,61,82,64,74,69,73,73,67,59,70", "endOffsets": "1712,2496,2641,2724,2789,3085,3630,3838,4080,4398,4746,5045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-pl-rPL\\values-pl-rPL.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,262,329,426,499,748,813,908,979,1033,1097,1401,1472,1539,1592,1645,1723,1850,1960,2017,2101,2178,2266,2337,2444,2529,2606,2680,2742,2805,2865,2960,3046,3149,3223,3302,3399,3579,3768,3881,3939,4633,4693", "endColumns": "159,46,66,96,72,248,64,94,70,53,63,303,70,66,52,52,77,126,109,56,83,76,87,70,106,84,76,73,61,62,59,94,85,102,73,78,96,179,188,112,57,693,59,54", "endOffsets": "210,257,324,421,494,743,808,903,974,1028,1092,1396,1467,1534,1587,1640,1718,1845,1955,2012,2096,2173,2261,2332,2439,2524,2601,2675,2737,2800,2860,2955,3041,3144,3218,3297,3394,3574,3763,3876,3934,4628,4688,4743"}, "to": {"startLines": "84,88,89,90,91,92,93,94,108,111,113,119,124,125,132,142,145,146,147,148,149,155,158,163,165,166,167,169,171,172,175,180,184,196,197,198,199,209,215,216,217,219,220,232", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6328,7350,7397,7464,7561,7634,7883,7948,9755,10012,10198,10640,11247,11318,11796,12739,12950,13028,13155,13265,13322,13881,14129,14607,14757,14864,14949,15088,15273,15335,15570,16234,16618,17790,17893,17967,18046,18956,19522,19711,19824,19953,20647,21610", "endColumns": "159,46,66,96,72,248,64,94,70,53,63,303,70,66,52,52,77,126,109,56,83,76,87,70,106,84,76,73,61,62,59,94,85,102,73,78,96,179,188,112,57,693,59,54", "endOffsets": "6483,7392,7459,7556,7629,7878,7943,8038,9821,10061,10257,10939,11313,11380,11844,12787,13023,13150,13260,13317,13401,13953,14212,14673,14859,14944,15021,15157,15330,15393,15625,16324,16699,17888,17962,18041,18138,19131,19706,19819,19877,20642,20702,21660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-pl-rPL\\values-pl-rPL.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,138,239,306,374,438,515", "endColumns": "82,100,66,67,63,76,70", "endOffsets": "133,234,301,369,433,510,581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-pl-rPL\\values-pl-rPL.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,141,237,369,481,575,686,863,1059,1202,1290,1392,1484,1579,1674,1736,1802,1896,2001,2226,2313,2395,2467,2547,2665,2754,2847,2914,2974,3077,3181,3312,3407,3478,3553,3650,3723,3830,3937", "endColumns": "85,95,131,111,93,110,176,195,142,87,101,91,94,94,61,65,93,104,224,86,81,71,79,117,88,92,66,59,102,103,130,94,70,74,96,72,106,106,93", "endOffsets": "136,232,364,476,570,681,858,1054,1197,1285,1387,1479,1574,1669,1731,1797,1891,1996,2221,2308,2390,2462,2542,2660,2749,2842,2909,2969,3072,3176,3307,3402,3473,3548,3645,3718,3825,3932,4026"}, "to": {"startLines": "17,95,112,122,123,170,176,177,178,179,181,182,183,185,186,187,188,189,190,191,192,193,194,195,200,201,202,203,204,205,206,207,208,226,233,234,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1138,8043,10066,11041,11153,15162,15630,15807,16003,16146,16329,16431,16523,16704,16799,16861,16927,17021,17126,17351,17438,17520,17592,17672,18143,18232,18325,18392,18452,18555,18659,18790,18885,21173,21665,21762,21835,21942,22049", "endColumns": "85,95,131,111,93,110,176,195,142,87,101,91,94,94,61,65,93,104,224,86,81,71,79,117,88,92,66,59,102,103,130,94,70,74,96,72,106,106,93", "endOffsets": "1219,8134,10193,11148,11242,15268,15802,15998,16141,16229,16426,16518,16613,16794,16856,16922,17016,17121,17346,17433,17515,17587,17667,17785,18227,18320,18387,18447,18550,18654,18785,18880,18951,21243,21757,21830,21937,22044,22138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-pl-rPL\\values-pl-rPL.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,196,264,355,425,486,558,625,689,757,840,900,962,1036,1100,1172,1234,1311,1380,1462,1541,1623,1712,1816,1898,1946,2000,2085,2149,2216,2288,2381,2468,2572", "endColumns": "62,77,67,90,69,60,71,66,63,67,82,59,61,73,63,71,61,76,68,81,78,81,88,103,81,47,53,84,63,66,71,92,86,103,97", "endOffsets": "113,191,259,350,420,481,553,620,684,752,835,895,957,1031,1095,1167,1229,1306,1375,1457,1536,1618,1707,1811,1893,1941,1995,2080,2144,2211,2283,2376,2463,2567,2665"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,109,110,120,130,131,133,140,141,150,151,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1576,1812,2065,2213,2304,2374,2876,2948,3090,3154,3222,3305,3365,3427,3501,3635,3707,3934,4828,4897,5894,6042,6124,9826,9930,10944,11657,11711,11849,12600,12667,13406,13499,14217,14321", "endColumns": "62,77,67,90,69,60,71,66,63,67,82,59,61,73,63,71,61,76,68,81,78,81,88,103,81,47,53,84,63,66,71,92,86,103,97", "endOffsets": "1634,1885,2128,2299,2369,2430,2943,3010,3149,3217,3300,3360,3422,3496,3560,3702,3764,4006,4892,4974,5968,6119,6208,9925,10007,10987,11706,11791,11908,12662,12734,13494,13581,14316,14414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-pl-rPL\\values-pl-rPL.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,196,258,329,409,471,532,584,659,737,807,902,997,1077,1157,1240,1322,1413,1504,1575,1663,1748,1841,1951,2028,2116,2194,2289,2368,2451,2542,2629,2706,2808,2872,2941,3056,3140,3204,3918,4581,4655,4766,4864,4919,5018,5119,5185,5284,5374,5442,5534,5583,5665,5768,5841,5912,5961,6040,6139,6186,6233,6310,6368,6436,6623,6789,6920,6985,7078,7154,7247,7326,7418,7514,7606,7685,7766,7857,7914,8050,8097,8153,8224,8288,8356,8424,8497,8586", "endColumns": "65,74,61,70,79,61,60,51,74,77,69,94,94,79,79,82,81,90,90,70,87,84,92,109,76,87,77,94,78,82,90,86,76,101,63,68,114,83,63,713,662,73,110,97,54,98,100,65,98,89,67,91,48,81,102,72,70,48,78,98,46,46,76,57,67,186,165,130,64,92,75,92,78,91,95,91,78,80,90,56,135,46,55,70,63,67,67,72,88,74", "endOffsets": "116,191,253,324,404,466,527,579,654,732,802,897,992,1072,1152,1235,1317,1408,1499,1570,1658,1743,1836,1946,2023,2111,2189,2284,2363,2446,2537,2624,2701,2803,2867,2936,3051,3135,3199,3913,4576,4650,4761,4859,4914,5013,5114,5180,5279,5369,5437,5529,5578,5660,5763,5836,5907,5956,6035,6134,6181,6228,6305,6363,6431,6618,6784,6915,6980,7073,7149,7242,7321,7413,7509,7601,7680,7761,7852,7909,8045,8092,8148,8219,8283,8351,8419,8492,8581,8656"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,85,86,87,96,97,98,99,100,101,102,103,104,105,106,107,114,115,116,117,118,121,126,127,128,129,134,135,136,137,138,139,143,144,153,154,156,157,161,162,164,173,174,211,212,213,214,218,227,228,229,230,231,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "586,652,727,789,860,940,1002,1301,1353,1428,1506,1717,1890,1985,2133,2501,2794,3843,4085,4176,4247,4403,4488,4581,4751,5050,5138,5216,5311,5390,5473,5564,5651,5728,5830,5973,6213,6488,6572,6636,8139,8802,8876,8987,9085,9140,9239,9340,9406,9505,9595,9663,10262,10311,10393,10496,10569,10992,11385,11464,11563,11610,11913,11990,12048,12116,12303,12469,12792,12857,13712,13788,13958,14037,14419,14515,14678,15398,15479,19226,19283,19419,19466,19882,21248,21312,21380,21448,21521,22857", "endColumns": "65,74,61,70,79,61,60,51,74,77,69,94,94,79,79,82,81,90,90,70,87,84,92,109,76,87,77,94,78,82,90,86,76,101,63,68,114,83,63,713,662,73,110,97,54,98,100,65,98,89,67,91,48,81,102,72,70,48,78,98,46,46,76,57,67,186,165,130,64,92,75,92,78,91,95,91,78,80,90,56,135,46,55,70,63,67,67,72,88,74", "endOffsets": "647,722,784,855,935,997,1058,1348,1423,1501,1571,1807,1980,2060,2208,2579,2871,3929,4171,4242,4330,4483,4576,4686,4823,5133,5211,5306,5385,5468,5559,5646,5723,5825,5889,6037,6323,6567,6631,7345,8797,8871,8982,9080,9135,9234,9335,9401,9500,9590,9658,9750,10306,10388,10491,10564,10635,11036,11459,11558,11605,11652,11985,12043,12111,12298,12464,12595,12852,12945,13783,13876,14032,14124,14510,14602,14752,15474,15565,19278,19414,19461,19517,19948,21307,21375,21443,21516,21605,22927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-pl-rPL\\values-pl-rPL.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,207,333,395,485,541,599,691,786,951,1025,1095,1177,1265,1387,1517,1590,1665,1834,1903,1962,2036,2115,2255,2372,2437,2541,2627,2704,2786,2858", "endColumns": "74,76,125,61,89,55,57,91,94,164,73,69,81,87,121,129,72,74,168,68,58,73,78,139,116,64,103,85,76,81,71,167", "endOffsets": "125,202,328,390,480,536,594,686,781,946,1020,1090,1172,1260,1382,1512,1585,1660,1829,1898,1957,2031,2110,2250,2367,2432,2536,2622,2699,2781,2853,3021"}, "to": {"startLines": "16,18,152,168,210,221,222,223,224,225,238,239,240,241,242,243,244,245,247,248,249,250,251,252,253,254,255,256,257,258,259,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1063,1224,13586,15026,19136,20707,20763,20821,20913,21008,22143,22217,22287,22369,22457,22579,22709,22782,22932,23101,23170,23229,23303,23382,23522,23639,23704,23808,23894,23971,24053,24125", "endColumns": "74,76,125,61,89,55,57,91,94,164,73,69,81,87,121,129,72,74,168,68,58,73,78,139,116,64,103,85,76,81,71,167", "endOffsets": "1133,1296,13707,15083,19221,20758,20816,20908,21003,21168,22212,22282,22364,22452,22574,22704,22777,22852,23096,23165,23224,23298,23377,23517,23634,23699,23803,23889,23966,24048,24120,24288"}}]}]}