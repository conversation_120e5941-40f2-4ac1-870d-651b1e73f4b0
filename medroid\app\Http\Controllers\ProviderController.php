<?php

namespace App\Http\Controllers;

use App\Models\Patient;
use App\Models\Provider;
use App\Models\User;
use App\Models\Appointment;
use App\Models\Clinic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;
use Inertia\Response;
use App\Services\ProviderService;
use Carbon\Carbon;

class ProviderController extends Controller
{
    protected $providerService;

    public function __construct(ProviderService $providerService)
    {
        $this->providerService = $providerService;
    }

    public function index(Request $request)
    {
        // Always include the user and services relationships
        $query = Provider::with(['user', 'services', 'clinic']);

        // Filter by specialization
        if ($request->has('specialization')) {
            $query->where('specialization', $request->specialization);
        }

        // Filter by services
        if ($request->has('service')) {
            $query->whereIn('services', [$request->service]);
        }

        // Filter by rating
        if ($request->has('min_rating')) {
            $query->where('rating', '>=', (float) $request->min_rating);
        }

        // Filter by clinic
        if ($request->has('clinic_id')) {
            $query->where('clinic_id', $request->clinic_id);
        }

        // Get the current user's appointment preferences if they are a patient
        $user = $request->user();
        $patientPreferences = null;
        $userLat = null;
        $userLng = null;
        $searchRadius = 25; // Default search radius in km

        if ($user && $user->role === 'patient') {
            $patient = Patient::where('user_id', $user->id)->first();
            if ($patient && !empty($patient->appointment_preferences)) {
                $patientPreferences = $patient->appointment_preferences;

                // Use patient's saved location if available
                if (!empty($patientPreferences['latitude']) && !empty($patientPreferences['longitude'])) {
                    $userLat = (float) $patientPreferences['latitude'];
                    $userLng = (float) $patientPreferences['longitude'];
                    $searchRadius = (int) ($patientPreferences['search_radius'] ?? 25);
                }
            }
        }

        // Override with request parameters if provided
        if ($request->has('latitude') && $request->has('longitude')) {
            $userLat = (float) $request->latitude;
            $userLng = (float) $request->longitude;
        }

        if ($request->has('search_radius')) {
            $searchRadius = (int) $request->search_radius;
        }

        // Sort by rating or proximity (if location provided)
        $sortBy = $request->get('sort_by', 'rating');
        if ($sortBy === 'rating') {
            $query->orderBy('rating', 'desc');
        } elseif ($sortBy === 'proximity' && $userLat !== null && $userLng !== null) {
            // This is a simplified version - in a real app we would use geospatial queries
            // For MongoDB, we could use $geoNear or $nearSphere
            $providers = $query->get()->map(function ($provider) use ($userLat, $userLng, $searchRadius) {
                // Calculate the closest practice location
                $minDistance = PHP_FLOAT_MAX;
                foreach ($provider->practice_locations as $location) {
                    if (isset($location['coordinates']) && count($location['coordinates']) == 2) {
                        $providerLat = (float) $location['coordinates'][0];
                        $providerLng = (float) $location['coordinates'][1];

                        // Calculate distance using Haversine formula (more accurate for geographic distances)
                        $earthRadius = 6371; // in kilometers
                        $latDelta = deg2rad($providerLat - $userLat);
                        $lngDelta = deg2rad($providerLng - $userLng);

                        $a = sin($latDelta/2) * sin($latDelta/2) +
                             cos(deg2rad($userLat)) * cos(deg2rad($providerLat)) *
                             sin($lngDelta/2) * sin($lngDelta/2);

                        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                        $distance = $earthRadius * $c;

                        if ($distance < $minDistance) {
                            $minDistance = $distance;
                        }
                    }
                }

                // Add distance to provider object
                $provider->distance = $minDistance;
                $provider->outside_preferred_radius = $minDistance > $searchRadius;

                return $provider;
            })->sortBy('distance');

            return response()->json([
                'providers' => $providers->values()->toArray(),
                'user_location' => [
                    'latitude' => $userLat,
                    'longitude' => $userLng,
                    'search_radius' => $searchRadius
                ]
            ]);
        }

        $providers = $query->paginate(10);

        return response()->json($providers);
    }

    public function show($id)
    {
        $provider = Provider::with(['user', 'clinic'])->findOrFail($id);

        return response()->json($provider);
    }

    public function getSpecializations()
    {
        $specializations = Provider::distinct('specialization')->pluck('specialization');

        return response()->json(['specializations' => $specializations]);
    }

    /**
     * Get the authenticated provider's profile
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getProfile(Request $request)
    {
        $user = $request->user();

        if ($user->role !== 'provider') {
            return response()->json([
                'message' => 'Only providers can access their profile'
            ], 403);
        }

        try {
            $provider = Provider::with('user')->where('user_id', $user->id)->firstOrFail();
            return response()->json($provider);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'message' => 'Provider profile not found',
                'error' => 'profile_not_found'
            ], 404);
        } catch (\Exception $e) {
            \Log::error('Error fetching provider profile: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error fetching provider profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create or update the authenticated provider's profile
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createOrUpdateProfile(Request $request)
    {
        try {
            $user = $request->user();

            if ($user->role !== 'provider') {
                return response()->json([
                    'message' => 'Only providers can create or update their profile'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'specialization' => 'required|string|max:255',
                'weekly_availability' => 'present|array',
                'weekly_availability.*.day' => 'nullable|string|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
                'weekly_availability.*.slots' => 'nullable|array',
                'absences' => 'nullable|array',
                'gender' => 'nullable|string|in:male,female,other',
                'bio' => 'nullable|string',
                'education' => 'nullable|string',
                'license_number' => 'nullable|string|max:255',
                'languages' => 'nullable|array',
                'languages.*' => 'string',
                'practice_locations' => 'nullable|array',
                'practice_locations.*.address' => 'required_with:practice_locations|string',
                'practice_locations.*.city' => 'nullable|string',
                'practice_locations.*.state' => 'nullable|string',
                'practice_locations.*.zip_code' => 'nullable|string',
                'practice_locations.*.coordinates' => 'nullable|array|size:2',
                'practice_locations.*.coordinates.*' => 'numeric',
                'practice_locations.*.is_primary' => 'nullable|boolean',
                'accepts_insurance' => 'nullable|boolean',
                'insurance_providers' => 'nullable|array',
                'insurance_providers.*' => 'string',
                'pricing' => 'nullable|array',
                'pricing.consultation' => 'nullable|numeric|min:0',
                'pricing.follow_up' => 'nullable|numeric|min:0',
                // Legacy fields for backward compatibility
                'phone' => 'nullable|string',
                'address' => 'nullable|string',
                'city' => 'nullable|string',
                'state' => 'nullable|string',
                'zip_code' => 'nullable|string',
                'consultation_fee' => 'nullable|numeric|min:0',
                'certifications' => 'nullable|array',
                'certifications.*' => 'string',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            // Ensure weekly_availability has proper structure
            $weeklyAvailability = $request->weekly_availability;

            // If empty or invalid, use default structure
            if (empty($weeklyAvailability) || !is_array($weeklyAvailability)) {
                $weeklyAvailability = [
                    ['day' => 'Monday', 'slots' => []],
                    ['day' => 'Tuesday', 'slots' => []],
                    ['day' => 'Wednesday', 'slots' => []],
                    ['day' => 'Thursday', 'slots' => []],
                    ['day' => 'Friday', 'slots' => []],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []],
                ];
            }

            $provider = Provider::where('user_id', $user->id)->first();

            if ($provider) {
                // Update existing provider profile
                $provider->specialization = $request->specialization;
                $provider->weekly_availability = $weeklyAvailability;
                $provider->absences = $request->absences ?? [];

                // Update all profile fields if provided
                $fieldsToUpdate = [
                    'gender', 'bio', 'education', 'license_number',
                    'languages', 'practice_locations', 'accepts_insurance', 'insurance_providers',
                    'pricing', 'certifications'
                ];

                foreach ($fieldsToUpdate as $field) {
                    if ($request->has($field)) {
                        $provider->$field = $request->$field;
                    }
                }

                // Handle legacy fields for backward compatibility
                if ($request->has('consultation_fee')) {
                    $pricing = $provider->pricing ?? [];
                    $pricing['consultation'] = $request->consultation_fee;
                    $provider->pricing = $pricing;
                }

                $provider->save();

                // Reload the provider with the user relationship
                $provider = Provider::with('user')->find($provider->id);

                return response()->json([
                    'message' => 'Provider profile updated successfully',
                    'provider' => $provider
                ]);
            } else {
                // Create new provider profile
                $provider = new Provider();
                $provider->user_id = $user->id;
                $provider->specialization = $request->specialization;
                $provider->weekly_availability = $weeklyAvailability;
                $provider->absences = $request->absences ?? [];
                $provider->verification_status = 'pending';
                $provider->rating = 0;

                // Set all profile fields if provided
                $fieldsToSet = [
                    'gender', 'bio', 'education', 'license_number',
                    'languages', 'practice_locations', 'accepts_insurance', 'insurance_providers',
                    'pricing', 'certifications'
                ];

                foreach ($fieldsToSet as $field) {
                    if ($request->has($field)) {
                        $provider->$field = $request->$field;
                    }
                }

                // Set defaults for some fields
                $provider->accepts_insurance = $provider->accepts_insurance ?? false;
                $provider->languages = $provider->languages ?? [];
                $provider->practice_locations = $provider->practice_locations ?? [];
                $provider->insurance_providers = $provider->insurance_providers ?? [];
                $provider->pricing = $provider->pricing ?? [];
                $provider->certifications = $provider->certifications ?? [];

                // Handle legacy fields for backward compatibility
                if ($request->has('consultation_fee')) {
                    $pricing = $provider->pricing ?? [];
                    $pricing['consultation'] = $request->consultation_fee;
                    $provider->pricing = $pricing;
                }

                $provider->save();

                // Reload the provider with the user relationship
                $provider = Provider::with('user')->find($provider->id);

                return response()->json([
                    'message' => 'Provider profile created successfully',
                    'provider' => $provider
                ], 201);
            }
        } catch (\Exception $e) {
            \Log::error('Error creating/updating provider profile: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return response()->json([
                'message' => 'Error creating or updating provider profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a provider's information
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $provider = Provider::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'specialization' => 'sometimes|string|max:255',
                'license_number' => 'sometimes|string|max:255',
                'verification_status' => 'sometimes|string|in:pending,verified,rejected',
                'gender' => 'sometimes|string|in:male,female,other',
                'bio' => 'sometimes|string',
                'education' => 'sometimes|string',
                'accepts_insurance' => 'sometimes|boolean',
                'insurance_providers' => 'sometimes|array',
                'practice_locations' => 'sometimes|array',
                'pricing' => 'sometimes|array',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $provider->update($request->only([
                'specialization',
                'license_number',
                'verification_status',
                'gender',
                'bio',
                'education',
                'accepts_insurance',
                'insurance_providers',
                'practice_locations',
                'pricing'
            ]));

            return response()->json([
                'message' => 'Provider updated successfully',
                'provider' => $provider->load('user')
            ]);

        } catch (\Exception $e) {
            \Log::error('Error updating provider: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error updating provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a provider's verification status
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateVerificationStatus(Request $request, $id)
    {
        try {
            $provider = Provider::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'status' => 'required|string|in:pending,verified,rejected',
                'rejection_reason' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $provider->verification_status = $request->status;

            if ($request->status === 'rejected' && $request->rejection_reason) {
                $provider->rejection_reason = $request->rejection_reason;
            } else {
                $provider->rejection_reason = null;
            }

            $provider->save();

            return response()->json([
                'message' => 'Verification status updated successfully',
                'provider' => $provider->load('user')
            ]);

        } catch (\Exception $e) {
            \Log::error('Error updating provider verification status: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error updating verification status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new provider
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'clinic_id' => 'nullable|exists:clinics,id',
                'specialization' => 'required|string|max:255',
                'license_number' => 'required|string|max:255|unique:providers,license_number',
                'gender' => 'nullable|string|in:male,female,other',
                'bio' => 'nullable|string',
                'education' => 'nullable|string',
                'accepts_insurance' => 'nullable|boolean',
                'insurance_providers' => 'nullable|array',
                'practice_locations' => 'nullable|array',
                'pricing' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            // Check if user already has a provider profile
            $existingProvider = Provider::where('user_id', $request->user_id)->first();
            if ($existingProvider) {
                return response()->json([
                    'message' => 'User already has a provider profile'
                ], 422);
            }

            // Get clinic_id or assign default clinic
            $clinicId = $request->clinic_id;
            if (!$clinicId) {
                $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
                if (!$defaultClinic) {
                    $defaultClinic = Clinic::first(); // Fallback to any clinic
                }
                $clinicId = $defaultClinic ? $defaultClinic->id : null;
            }

            $provider = Provider::create([
                'user_id' => $request->user_id,
                'clinic_id' => $clinicId,
                'specialization' => $request->specialization,
                'license_number' => $request->license_number,
                'verification_status' => 'verified', // Auto-verify admin-created providers
                'verified_at' => now(),
                'gender' => $request->gender,
                'bio' => $request->bio,
                'education' => $request->education,
                'accepts_insurance' => $request->accepts_insurance ?? false,
                'insurance_providers' => $request->insurance_providers ?? [],
                'practice_locations' => $request->practice_locations ?? [],
                'pricing' => $request->pricing ?? [],
                'rating' => 0,
                'weekly_availability' => [
                    ['day' => 'Monday', 'slots' => []],
                    ['day' => 'Tuesday', 'slots' => []],
                    ['day' => 'Wednesday', 'slots' => []],
                    ['day' => 'Thursday', 'slots' => []],
                    ['day' => 'Friday', 'slots' => []],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []],
                ],
                'absences' => [],
            ]);

            return response()->json([
                'message' => 'Provider created successfully',
                'provider' => $provider->load('user')
            ], 201);

        } catch (\Exception $e) {
            \Log::error('Error creating provider: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error creating provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new provider with user creation
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function storeWithUser(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                // User fields
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'phone_number' => 'nullable|string|max:20',
                'password' => 'required|string|min:8',
                // Provider fields
                'clinic_id' => 'nullable|exists:clinics,id',
                'specialization' => 'required|string|max:255',
                'license_number' => 'required|string|max:255|unique:providers,license_number',
                'gender' => 'nullable|string|in:male,female,other',
                'bio' => 'nullable|string',
                'education' => 'nullable|string',
                'accepts_insurance' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            DB::beginTransaction();

            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => Hash::make($request->password),
                'role' => 'provider',
                'email_verified_at' => now(), // Auto-verify for admin-created accounts
            ]);

            // Assign provider role
            $user->assignRole('provider');

            // Get clinic or assign default
            $clinicId = $request->clinic_id;
            if (!$clinicId) {
                $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
                if (!$defaultClinic) {
                    $defaultClinic = Clinic::first();
                }
                $clinicId = $defaultClinic ? $defaultClinic->id : null;
            }

            // Create provider profile
            $provider = Provider::create([
                'user_id' => $user->id,
                'clinic_id' => $clinicId,
                'specialization' => $request->specialization,
                'license_number' => $request->license_number,
                'verification_status' => 'verified', // Auto-verify admin-created providers
                'verified_at' => now(),
                'gender' => $request->gender,
                'bio' => $request->bio,
                'education' => $request->education,
                'accepts_insurance' => $request->accepts_insurance ?? false,
                'rating' => 0,
                'weekly_availability' => [
                    ['day' => 'Monday', 'slots' => []],
                    ['day' => 'Tuesday', 'slots' => []],
                    ['day' => 'Wednesday', 'slots' => []],
                    ['day' => 'Thursday', 'slots' => []],
                    ['day' => 'Friday', 'slots' => []],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []],
                ],
                'absences' => [],
                'languages' => [],
                'practice_locations' => [],
                'insurance_providers' => [],
                'pricing' => [],
                'certifications' => [],
            ]);

            DB::commit();

            // Load relationships
            $provider->load(['user', 'clinic']);

            return response()->json([
                'message' => 'Provider created successfully',
                'provider' => $provider
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating provider with user: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error creating provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Public endpoint to get providers without authentication
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function publicIndex(Request $request)
    {
        // Always include the user and services relationships
        $query = Provider::with(['user', 'services']);

        // Filter by specialization
        if ($request->has('specialization')) {
            $query->where('specialization', $request->specialization);
        }

        // Filter by services
        if ($request->has('service')) {
            $query->whereIn('services', [$request->service]);
        }

        // Filter by rating
        if ($request->has('min_rating')) {
            $query->where('rating', '>=', (float) $request->min_rating);
        }

        // Get all providers
        $providers = $query->get();

        return response()->json([
            'providers' => $providers
        ]);
    }

    /**
     * Display the providers page (web route)
     */
    public function webIndex(Request $request): Response
    {
        return Inertia::render('Providers');
    }

    /**
     * Display a specific provider (web route)
     */
    public function webShow(Request $request, Provider $provider): Response
    {
        $providerData = $this->providerService->getProvider($provider->id);

        if (!$providerData) {
            abort(404);
        }

        return Inertia::render('ProviderDetail', [
            'provider' => $providerData
        ]);
    }

    /**
     * Get providers for API (using service)
     */
    public function getProvidersApi(Request $request)
    {
        $filters = [
            'search' => $request->input('search'),
            'specialization' => $request->input('specialization'),
            'location' => $request->input('location'),
            'min_rating' => $request->input('min_rating')
        ];

        $perPage = $request->input('per_page', 15);
        $providers = $this->providerService->getProviders($filters, $perPage);

        return response()->json([
            'providers' => $providers
        ]);
    }

    /**
     * Get provider specializations
     */
    public function getSpecializationsApi(Request $request)
    {
        $specializations = $this->providerService->getSpecializations();

        return response()->json([
            'specializations' => $specializations
        ]);
    }

    /**
     * Get provider dashboard data
     */
    public function getDashboardData(Request $request)
    {
        try {
            $user = Auth::user();
            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            // Get today's appointments
            $today = Carbon::today();
            $todayAppointments = Appointment::where('provider_id', $provider->id)
                ->whereDate('scheduled_at', $today)
                ->with(['patient.user', 'service'])
                ->orderBy('scheduled_at')
                ->get();

            // Get upcoming appointments (next 7 days)
            $upcomingAppointments = Appointment::where('provider_id', $provider->id)
                ->whereDate('scheduled_at', '>', $today)
                ->whereDate('scheduled_at', '<=', $today->copy()->addDays(7))
                ->with(['patient.user', 'service'])
                ->orderBy('scheduled_at')
                ->limit(5)
                ->get();

            // Get total patients count
            $totalPatients = Appointment::where('provider_id', $provider->id)
                ->distinct('patient_id')
                ->count('patient_id');

            // Get monthly earnings
            $startOfMonth = Carbon::now()->startOfMonth();
            $endOfMonth = Carbon::now()->endOfMonth();
            $monthlyEarnings = Appointment::where('provider_id', $provider->id)
                ->whereBetween('scheduled_at', [$startOfMonth, $endOfMonth])
                ->where('status', 'completed')
                ->sum('amount');

            // Get completion rate for this month
            $totalAppointmentsThisMonth = Appointment::where('provider_id', $provider->id)
                ->whereBetween('scheduled_at', [$startOfMonth, $endOfMonth])
                ->count();

            $completedAppointmentsThisMonth = Appointment::where('provider_id', $provider->id)
                ->whereBetween('scheduled_at', [$startOfMonth, $endOfMonth])
                ->where('status', 'completed')
                ->count();

            $completionRate = $totalAppointmentsThisMonth > 0
                ? round(($completedAppointmentsThisMonth / $totalAppointmentsThisMonth) * 100, 1)
                : 0;

            return response()->json([
                'stats' => [
                    'totalPatients' => $totalPatients,
                    'upcomingAppointments' => $upcomingAppointments->count(),
                    'monthlyEarnings' => $monthlyEarnings,
                    'completionRate' => $completionRate
                ],
                'todayAppointments' => $todayAppointments,
                'upcomingAppointments' => $upcomingAppointments,
                'provider' => $provider
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching provider dashboard data: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error fetching dashboard data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get provider appointments
     */
    public function getAppointments(Request $request)
    {
        try {
            $user = Auth::user();
            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            $query = Appointment::where('provider_id', $provider->id)
                ->with(['patient.user', 'service']);

            // Filter by date if provided
            if ($request->has('date')) {
                $query->whereDate('scheduled_at', $request->date);
            }

            // Filter by status if provided
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Default to upcoming appointments if no filters
            if (!$request->has('date') && !$request->has('status')) {
                $query->whereDate('scheduled_at', '>=', Carbon::today());
            }

            $appointments = $query->orderBy('scheduled_at')->get();

            return response()->json([
                'appointments' => $appointments
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching provider appointments: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error fetching appointments',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get provider's patients
     */
    public function getPatients(Request $request)
    {
        try {
            $user = Auth::user();
            $provider = Provider::where('user_id', $user->id)->first();

            if (!$provider) {
                return response()->json([
                    'message' => 'Provider profile not found'
                ], 404);
            }

            // Get unique patients who have had appointments with this provider
            $patients = Patient::whereHas('appointments', function ($query) use ($provider) {
                $query->where('provider_id', $provider->id);
            })
            ->with(['user', 'appointments' => function ($query) use ($provider) {
                $query->where('provider_id', $provider->id)
                      ->orderBy('scheduled_at', 'desc');
            }])
            ->get()
            ->map(function ($patient) {
                // Add last appointment date
                $lastAppointment = $patient->appointments->first();
                $patient->last_appointment = $lastAppointment ? $lastAppointment->scheduled_at : null;

                return $patient;
            });

            return response()->json([
                'patients' => $patients,
                'total' => $patients->count()
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching provider patients: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error fetching patients',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the provider availability page.
     */
    public function availability(): Response
    {
        return Inertia::render('Provider/Availability');
    }

    /**
     * Display the provider services page.
     */
    public function services(): Response
    {
        return Inertia::render('Provider/Services');
    }

    /**
     * Display the provider schedule page.
     */
    public function schedule(): Response
    {
        return Inertia::render('Provider/Schedule');
    }

    /**
     * Display the provider patients page.
     */
    public function patients(): Response
    {
        return Inertia::render('Provider/Patients');
    }

    /**
     * Display the provider earnings page.
     */
    public function earnings(): Response
    {
        return Inertia::render('Provider/Earnings');
    }

    /**
     * Display the provider profile page.
     */
    public function profile(): Response
    {
        return Inertia::render('Provider/Profile');
    }

    /**
     * Display the provider products page.
     */
    public function products(): Response
    {
        return Inertia::render('Provider/Products');
    }
}