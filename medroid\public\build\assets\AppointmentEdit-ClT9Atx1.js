import{r as h,B as F,d as n,e as o,f as w,u as _,m as N,g as x,i as t,z as V,l as c,j as u,v as f,t as r,p as j,x as A,F as T,q as B,y as E,P,W as S}from"./vendor-CKE3WRFf.js";import{_ as U}from"./AppLayout.vue_vue_type_script_setup_true_lang-Dvt6ywSf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B8nQAqQg.js";import"./createLucideIcon-Dy3o-9bT.js";const D={class:"flex items-center justify-between"},M={class:"flex mt-2","aria-label":"Breadcrumb"},$={class:"inline-flex items-center space-x-1 md:space-x-3"},O={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},I={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},L={class:"py-12"},R={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},W={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},X={class:"p-6"},z={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},H={class:"space-y-4"},J=["min"],K={key:0,class:"mt-1 text-sm text-red-600"},G={key:0,class:"mt-1 text-sm text-red-600"},Q={key:0,class:"mt-1 text-sm text-red-600"},Y={class:"space-y-4"},Z={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},tt={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},et={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},st={key:0,class:"mt-1 text-sm text-red-600"},at={class:"mt-6 flex justify-end space-x-3"},ot=["disabled"],it={key:0,class:"fas fa-spinner fa-spin mr-2"},nt={key:1,class:"fas fa-save mr-2"},ut={__name:"AppointmentEdit",props:{appointment:{type:Object,required:!0}},setup(m){var b,v,k;const d=m,g=[{title:"Dashboard",href:"/dashboard"},{title:"Appointments",href:"/appointments"},{title:"Edit Appointment",href:"#"}],p=h(!1),a=F({date:d.appointment.date||((b=d.appointment.scheduled_at)==null?void 0:b.split("T")[0])||"",time:d.appointment.time||((k=(v=d.appointment.scheduled_at)==null?void 0:v.split("T")[1])==null?void 0:k.substring(0,5))||"",notes:d.appointment.notes||"",type:d.appointment.is_telemedicine?"telemedicine":"in-person"}),i=h({}),q=async()=>{var y;p.value=!0,i.value={};try{const e=(y=document.querySelector('meta[name="csrf-token"]'))==null?void 0:y.getAttribute("content"),l=await fetch(`/save-appointment/${d.appointment.id}`,{method:"PUT",headers:{Accept:"application/json","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":e||""},credentials:"same-origin",body:JSON.stringify({date:a.date,time_slot:{start_time:a.time,end_time:a.time},notes:a.notes,is_telemedicine:a.type==="telemedicine"})}),s=await l.json().catch(()=>({}));l.ok?(alert("Appointment updated successfully."),S.visit(`/appointments/${d.appointment.id}`)):s.errors?i.value=s.errors:alert(s.message||"Failed to update appointment. Please try again.")}catch(e){console.error("Error updating appointment:",e),alert("Failed to update appointment. Please try again.")}finally{p.value=!1}},C=()=>{S.visit(`/appointments/${d.appointment.id}`)};return(y,e)=>(o(),n(T,null,[w(_(N),{title:"Edit Appointment"}),w(U,null,{header:x(()=>[t("div",D,[t("div",null,[e[5]||(e[5]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Edit Appointment ",-1)),t("nav",M,[t("ol",$,[(o(),n(T,null,B(g,(l,s)=>t("li",{key:s,class:"inline-flex items-center"},[s<g.length-1?(o(),E(_(P),{key:0,href:l.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:x(()=>[A(r(l.title),1)]),_:2},1032,["href"])):(o(),n("span",O,r(l.title),1)),s<g.length-1?(o(),n("svg",I,e[4]||(e[4]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):u("",!0)])),64))])])])])]),default:x(()=>{var l;return[t("div",L,[t("div",R,[t("div",W,[t("div",X,[t("form",{onSubmit:V(q,["prevent"])},[t("div",z,[t("div",null,[e[10]||(e[10]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"}," Appointment Details ",-1)),t("div",H,[t("div",null,[e[6]||(e[6]=t("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Date * ",-1)),c(t("input",{id:"date","onUpdate:modelValue":e[0]||(e[0]=s=>a.date=s),type:"date",min:new Date().toISOString().split("T")[0],class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:""},null,8,J),[[f,a.date]]),i.value.date?(o(),n("p",K,r(i.value.date[0]),1)):u("",!0)]),t("div",null,[e[7]||(e[7]=t("label",{for:"time",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Time * ",-1)),c(t("input",{id:"time","onUpdate:modelValue":e[1]||(e[1]=s=>a.time=s),type:"time",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:""},null,512),[[f,a.time]]),i.value.time_slot?(o(),n("p",G,r(i.value.time_slot[0]),1)):u("",!0)]),t("div",null,[e[9]||(e[9]=t("label",{for:"type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Type * ",-1)),c(t("select",{id:"type","onUpdate:modelValue":e[2]||(e[2]=s=>a.type=s),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:""},e[8]||(e[8]=[t("option",{value:"telemedicine"},"Telemedicine",-1),t("option",{value:"in-person"},"In-Person",-1)]),512),[[j,a.type]]),i.value.is_telemedicine?(o(),n("p",Q,r(i.value.is_telemedicine[0]),1)):u("",!0)])])]),t("div",null,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"}," Additional Information ",-1)),t("div",Y,[t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Service",-1)),t("p",Z,r(m.appointment.service||m.appointment.reason||"Consultation"),1)]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Provider",-1)),t("p",tt,r(m.appointment.provider_name||((l=m.appointment.provider)==null?void 0:l.name)||"N/A"),1)]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Amount",-1)),t("p",et,"$"+r(typeof m.appointment.amount=="number"?m.appointment.amount.toFixed(2):parseFloat(m.appointment.amount||0).toFixed(2)),1)]),t("div",null,[e[14]||(e[14]=t("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Notes ",-1)),c(t("textarea",{id:"notes","onUpdate:modelValue":e[3]||(e[3]=s=>a.notes=s),rows:"4",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Add any additional notes..."},null,512),[[f,a.notes]]),i.value.notes?(o(),n("p",st,r(i.value.notes[0]),1)):u("",!0)])])])]),t("div",at,[t("button",{type:"button",onClick:C,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"}," Cancel "),t("button",{type:"submit",disabled:p.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"},[p.value?(o(),n("i",it)):(o(),n("i",nt)),A(" "+r(p.value?"Updating...":"Update Appointment"),1)],8,ot)])],32)])])])])]}),_:1})],64))}};export{ut as default};
