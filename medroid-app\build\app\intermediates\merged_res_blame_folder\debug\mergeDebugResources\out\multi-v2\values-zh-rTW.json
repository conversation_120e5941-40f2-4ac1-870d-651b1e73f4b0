{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4363,4464,4592,4707,4809,4916,5032,5132,5330,5440,5541,5670,5785,5887,5995,6051,6108", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "4459,4587,4702,4804,4911,5027,5127,5222,5435,5536,5665,5780,5882,5990,6046,6103,6177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2430,2480,2531,2597,2660,2728,2806,2867,2938,3005,3067,3146,3211,3294,3379,3453,3517,3593,3641,3705,3781,3859,3921,3985,4048,4128,4205,4281,4358,4412,4467", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,76,49,50,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2425,2475,2526,2592,2655,2723,2801,2862,2933,3000,3062,3141,3206,3289,3374,3448,3512,3588,3636,3700,3776,3854,3916,3980,4043,4123,4200,4276,4353,4407,4462,4531"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2863,2927,2989,3056,3126,3864,3958,4065,6658,7431,7856,8067,8127,8205,8266,8324,8380,8440,8498,8552,8637,8693,8751,8805,8870,8962,9036,9113,9233,9296,9359,9436,9486,9537,9603,9666,9734,9812,9873,9944,10011,10073,10152,10217,10300,10385,10459,10523,10599,10647,10711,10787,10865,10927,10991,11054,11134,11211,11287,11364,11418,11826", "endLines": "5,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,76,49,50,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,53,54,68", "endOffsets": "292,2922,2984,3051,3121,3198,3953,4060,4133,6715,7504,7911,8122,8200,8261,8319,8375,8435,8493,8547,8632,8688,8746,8800,8865,8957,9031,9108,9228,9291,9354,9431,9481,9532,9598,9661,9729,9807,9868,9939,10006,10068,10147,10212,10295,10380,10454,10518,10594,10642,10706,10782,10860,10922,10986,11049,11129,11206,11282,11359,11413,11468,11890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3203,3295,3394,3488,3582,3675,3768,12310", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3290,3389,3483,3577,3670,3763,3859,12406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1333,1406,1471,1542,1614,1677,1722,1768,1830,1892,1945,2007,2079,2146,2216", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1328,1401,1466,1537,1609,1672,1717,1763,1825,1887,1940,2002,2074,2141,2211,2280"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,264,265,275,285,286,288,295,296,305,306,314,315", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13751,13966,14194,14327,14400,14465,14915,14978,15101,15161,15229,15293,15354,15412,15478,15597,15662,15867,16641,16700,17557,17697,17762,20203,20275,20997,21576,21622,21735,22230,22283,22876,22948,23479,23549", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "13806,14032,14253,14395,14460,14521,14973,15034,15156,15224,15288,15349,15407,15473,15535,15657,15715,15929,16695,16767,17625,17757,17828,20270,20333,21037,21617,21679,21792,22278,22340,22943,23010,23544,23613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be80f888898887bb71616e48bf7874ba\\transformed\\jetified-ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "48,49,71,72,74,93,94,145,146,148,149,152,153,155,417,418,419", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4138,4215,6332,6419,6580,7916,7990,11473,11551,11696,11761,12015,12088,12242,31209,31283,31351", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "4210,4286,6414,6505,6653,7985,8062,11546,11621,11756,11821,12083,12158,12305,31278,31346,31462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "70,88,89,90", "startColumns": "4,4,4,4", "startOffsets": "6248,7509,7601,7702", "endColumns": "83,91,100,92", "endOffsets": "6327,7596,7697,7790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,175,247,298,361,411,462,520,589,707,775,840,903,978,1056,1140,1205,1268,1381,1446,1499,1561,1627,1712,1798,1857,1935,2006,2073,2136,2196", "endColumns": "58,60,71,50,62,49,50,57,68,117,67,64,62,74,77,83,64,62,112,64,52,61,65,84,85,58,77,70,66,62,59,93", "endOffsets": "109,170,242,293,356,406,457,515,584,702,770,835,898,973,1051,1135,1200,1263,1376,1441,1494,1556,1622,1707,1793,1852,1930,2001,2068,2131,2191,2285"}, "to": {"startLines": "171,173,307,323,365,376,377,378,379,380,393,394,395,396,397,398,399,400,402,403,404,405,406,407,408,409,410,411,412,413,414,415", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13318,13443,23015,24083,27384,28265,28315,28366,28424,28493,29396,29464,29529,29592,29667,29745,29829,29894,30019,30132,30197,30250,30312,30378,30463,30549,30608,30686,30757,30824,30887,30947", "endColumns": "58,60,71,50,62,49,50,57,68,117,67,64,62,74,77,83,64,62,112,64,52,61,65,84,85,58,77,70,66,62,59,93", "endOffsets": "13372,13499,23082,24129,27442,28310,28361,28419,28488,28606,29459,29524,29587,29662,29740,29824,29889,29952,30127,30192,30245,30307,30373,30458,30544,30603,30681,30752,30819,30882,30942,31036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e71e7c08b297cb32539b89f832f53d8\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,197", "endColumns": "71,69,70", "endOffsets": "122,192,263"}, "to": {"startLines": "50,73,86", "startColumns": "4,4,4", "startOffsets": "4291,6510,7360", "endColumns": "71,69,70", "endOffsets": "4358,6575,7426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,245,306,375,429,489,537,602,668,736,820,904,977,1046,1116,1187,1267,1346,1409,1485,1558,1633,1721,1791,1867,1937,2020,2085,2160,2233,2302,2371,2453,2513,2580,2672,2735,2796,3123,3441,3506,3590,3670,3725,3801,3873,3931,4002,4075,4130,4200,4245,4315,4397,4454,4519,4563,4627,4704,4747,4790,4845,4903,4961,5052,5144,5223,5283,5346,5405,5480,5543,5603,5665,5736,5794,5865,5942,5991,6066,6111,6161,6217,6273,6334,6393,6454,6525", "endColumns": "59,67,61,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,74,87,69,75,69,82,64,74,72,68,68,81,59,66,91,62,60,326,317,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,43,63,76,42,42,54,57,57,90,91,78,59,62,58,74,62,59,61,70,57,70,76,48,74,44,49,55,55,60,58,60,70,61", "endOffsets": "110,178,240,301,370,424,484,532,597,663,731,815,899,972,1041,1111,1182,1262,1341,1404,1480,1553,1628,1716,1786,1862,1932,2015,2080,2155,2228,2297,2366,2448,2508,2575,2667,2730,2791,3118,3436,3501,3585,3665,3720,3796,3868,3926,3997,4070,4125,4195,4240,4310,4392,4449,4514,4558,4622,4699,4742,4785,4840,4898,4956,5047,5139,5218,5278,5341,5400,5475,5538,5598,5660,5731,5789,5860,5937,5986,6061,6106,6156,6212,6268,6329,6388,6449,6520,6582"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,240,241,242,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,272,273,276,281,282,283,284,289,290,291,292,293,294,298,299,308,309,311,312,316,317,319,328,329,366,367,368,369,373,382,383,384,385,386,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12884,12944,13012,13074,13135,13204,13258,13504,13552,13617,13683,13882,14037,14121,14258,14583,14844,15787,16000,16079,16142,16278,16351,16426,16571,16835,16911,16981,17064,17129,17204,17277,17346,17415,17497,17630,17833,18060,18123,18184,19070,19388,19453,19537,19617,19672,19748,19820,19878,19949,20022,20077,20534,20579,20649,20731,20788,21042,21349,21413,21490,21533,21797,21852,21910,21968,22059,22151,22396,22456,23087,23146,23289,23352,23618,23680,23811,24377,24448,27447,27496,27571,27616,27916,28671,28727,28788,28847,28908,29957", "endColumns": "59,67,61,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,74,87,69,75,69,82,64,74,72,68,68,81,59,66,91,62,60,326,317,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,43,63,76,42,42,54,57,57,90,91,78,59,62,58,74,62,59,61,70,57,70,76,48,74,44,49,55,55,60,58,60,70,61", "endOffsets": "12939,13007,13069,13130,13199,13253,13313,13547,13612,13678,13746,13961,14116,14189,14322,14648,14910,15862,16074,16137,16213,16346,16421,16509,16636,16906,16976,17059,17124,17199,17272,17341,17410,17492,17552,17692,17920,18118,18179,18506,19383,19448,19532,19612,19667,19743,19815,19873,19944,20017,20072,20142,20574,20644,20726,20783,20848,21081,21408,21485,21528,21571,21847,21905,21963,22054,22146,22225,22451,22514,23141,23216,23347,23407,23675,23746,23864,24443,24520,27491,27566,27611,27661,27967,28722,28783,28842,28903,28974,30014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,12163", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,12237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13811,14526,14653,14713,14786,15039,15540,15720,15934,16218,16514,16772", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "13877,14578,14708,14781,14839,15096,15592,15782,15995,16273,16566,16830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,194,272,343,415,487,605,727,822,907,989,1073,1159,1238,1295,1354,1432,1517,1638,1708,1781,1849,1912,2000,2081,2162,2226,2282,2365,2449,2549,2628,2695,2755,2820,2879,2959,3044", "endColumns": "65,72,77,70,71,71,117,121,94,84,81,83,85,78,56,58,77,84,120,69,72,67,62,87,80,80,63,55,82,83,99,78,66,59,64,58,79,84,72", "endOffsets": "116,189,267,338,410,482,600,722,817,902,984,1068,1154,1233,1290,1349,1427,1512,1633,1703,1776,1844,1907,1995,2076,2157,2221,2277,2360,2444,2544,2623,2690,2750,2815,2874,2954,3039,3112"}, "to": {"startLines": "172,250,267,277,278,325,331,332,333,334,336,337,338,340,341,342,343,344,345,346,347,348,349,350,355,356,357,358,359,360,361,362,363,381,388,389,390,391,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13377,18997,20392,21086,21157,24188,24583,24701,24823,24918,25093,25175,25259,25422,25501,25558,25617,25695,25780,25901,25971,26044,26112,26175,26588,26669,26750,26814,26870,26953,27037,27137,27216,28611,29034,29099,29158,29238,29323", "endColumns": "65,72,77,70,71,71,117,121,94,84,81,83,85,78,56,58,77,84,120,69,72,67,62,87,80,80,63,55,82,83,99,78,66,59,64,58,79,84,72", "endOffsets": "13438,19065,20465,21152,21224,24255,24696,24818,24913,24998,25170,25254,25340,25496,25553,25612,25690,25775,25896,25966,26039,26107,26170,26258,26664,26745,26809,26865,26948,27032,27132,27211,27278,28666,29094,29153,29233,29318,29391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,60", "endOffsets": "125,209,274,340,400,462,523"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12411,12486,12570,12635,12701,12761,12823", "endColumns": "74,83,64,65,59,61,60", "endOffsets": "12481,12565,12630,12696,12756,12818,12879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,689", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "166,247,317,437,605,684,760"}, "to": {"startLines": "69,85,147,151,416,420,421", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6182,7279,11626,11895,31041,31467,31546", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "6243,7355,11691,12010,31204,31541,31617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba2bbf823ab5a18be680588e75f7e6ba\\transformed\\jetified-play-services-wallet-19.2.1\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "91,422", "startColumns": "4,4", "startOffsets": "7795,31622", "endColumns": "60,71", "endOffsets": "7851,31689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5227", "endColumns": "102", "endOffsets": "5325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,224,277,347,401,477,555", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "106,162,219,272,342,396,472,550,609"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6720,6776,6832,6889,6942,7012,7066,7142,7220", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "6771,6827,6884,6937,7007,7061,7137,7215,7274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,234,292,367,431,549,604,676,732,786,850,994,1054,1114,1165,1216,1282,1369,1446,1501,1573,1641,1708,1768,1843,1910,1982,2036,2094,2153,2211,2301,2378,2469,2539,2618,2703,2804,2913,3005,3054,3290,3347", "endColumns": "134,43,57,74,63,117,54,71,55,53,63,143,59,59,50,50,65,86,76,54,71,67,66,59,74,66,71,53,57,58,57,89,76,90,69,78,84,100,108,91,48,235,56,54", "endOffsets": "185,229,287,362,426,544,599,671,727,781,845,989,1049,1109,1160,1211,1277,1364,1441,1496,1568,1636,1703,1763,1838,1905,1977,2031,2089,2148,2206,2296,2373,2464,2534,2613,2698,2799,2908,3000,3049,3285,3342,3397"}, "to": {"startLines": "239,243,244,245,246,247,248,249,263,266,268,274,279,280,287,297,300,301,302,303,304,310,313,318,320,321,322,324,326,327,330,335,339,351,352,353,354,364,370,371,372,374,375,387", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17925,18511,18555,18613,18688,18752,18870,18925,20147,20338,20470,20853,21229,21289,21684,22345,22519,22585,22672,22749,22804,23221,23412,23751,23869,23944,24011,24134,24260,24318,24525,25003,25345,26263,26354,26424,26503,27283,27666,27775,27867,27972,28208,28979", "endColumns": "134,43,57,74,63,117,54,71,55,53,63,143,59,59,50,50,65,86,76,54,71,67,66,59,74,66,71,53,57,58,57,89,76,90,69,78,84,100,108,91,48,235,56,54", "endOffsets": "18055,18550,18608,18683,18747,18865,18920,18992,20198,20387,20529,20992,21284,21344,21730,22391,22580,22667,22744,22799,22871,23284,23474,23806,23939,24006,24078,24183,24313,24372,24578,25088,25417,26349,26419,26498,26583,27379,27770,27862,27911,28203,28260,29029"}}]}]}