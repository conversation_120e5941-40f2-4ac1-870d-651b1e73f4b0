<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link } from '@inertiajs/vue3'
import { ref, computed } from 'vue'
import axios from 'axios'

const props = defineProps({
    product: Object,
    relatedProducts: Array,
})

const breadcrumbs = computed(() => [
    { title: 'Shop', href: '/shop' },
    { title: props.product?.category?.name || 'Products', href: `/shop?category=${props.product?.category?.id}` },
    { title: props.product?.name || 'Product', href: '#' },
])

// Reactive data
const quantity = ref(1)
const selectedImage = ref(0)
const addingToCart = ref(false)

// Computed properties
const currentImage = computed(() => {
    if (props.product?.images?.length > 0) {
        return props.product.images[selectedImage.value]?.image_path || props.product.primary_image
    }
    return props.product?.primary_image || '/images/placeholder-product.jpg'
})

const totalPrice = computed(() => {
    return (props.product?.effective_price || 0) * quantity.value
})

const formattedTotalPrice = computed(() => {
    return '$' + totalPrice.value.toFixed(2)
})

// Methods
const addToCart = async () => {
    if (!props.product?.can_purchase) {
        alert('Product is not available for purchase')
        return
    }

    addingToCart.value = true
    try {
        const response = await axios.post('/shop/cart/add', {
            product_id: props.product.id,
            quantity: quantity.value
        })
        
        if (response.data.success) {
            alert('Product added to cart!')
            // Optionally redirect to cart or show success message
        } else {
            alert(response.data.message || 'Failed to add product to cart')
        }
    } catch (error) {
        console.error('Error adding to cart:', error)
        alert('Failed to add product to cart')
    } finally {
        addingToCart.value = false
    }
}

const increaseQuantity = () => {
    if (props.product?.type === 'physical' && props.product?.manage_stock) {
        if (quantity.value < props.product.stock_quantity) {
            quantity.value++
        }
    } else {
        quantity.value++
    }
}

const decreaseQuantity = () => {
    if (quantity.value > 1) {
        quantity.value--
    }
}
</script>

<template>
    <Head :title="`${product?.name || 'Product'} - Medroid Shop`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Product Images -->
                            <div class="space-y-4">
                                <!-- Main Image -->
                                <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200 relative">
                                    <img
                                        :src="currentImage"
                                        :alt="product?.name"
                                        class="h-96 w-full object-cover object-center"
                                    />
                                    <div v-if="product?.is_on_sale" class="absolute top-4 left-4 bg-red-500 text-white text-sm px-3 py-1 rounded shadow-md">
                                        {{ product.discount_percentage }}% OFF
                                    </div>
                                    <div v-if="product?.type === 'digital'" class="absolute top-4 right-4">
                                        <span class="bg-purple-500 text-white text-sm px-3 py-1 rounded shadow-md">
                                            Digital Product
                                        </span>
                                    </div>
                                </div>

                                <!-- Thumbnail Images -->
                                <div v-if="product?.images?.length > 1" class="flex space-x-2 overflow-x-auto">
                                    <button
                                        v-for="(image, index) in product.images"
                                        :key="index"
                                        @click="selectedImage = index"
                                        :class="[
                                            'flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors',
                                            selectedImage === index ? 'border-blue-500' : 'border-gray-200 hover:border-gray-300'
                                        ]"
                                    >
                                        <img
                                            :src="image.image_path"
                                            :alt="image.alt_text || product.name"
                                            class="w-full h-full object-cover"
                                        />
                                    </button>
                                </div>
                            </div>

                            <!-- Product Info -->
                            <div class="space-y-6">
                                <!-- Title and Category -->
                                <div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <Link 
                                            :href="`/shop?category=${product?.category?.id}`"
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                        >
                                            {{ product?.category?.name }}
                                        </Link>
                                        <span class="text-gray-400">•</span>
                                        <span class="text-gray-500 text-sm">SKU: {{ product?.sku }}</span>
                                    </div>
                                    <h1 class="text-3xl font-bold text-gray-900">{{ product?.name }}</h1>
                                </div>

                                <!-- Price -->
                                <div class="flex items-center space-x-3">
                                    <span class="text-3xl font-bold text-blue-600">{{ product?.formatted_price }}</span>
                                    <span v-if="product?.is_on_sale" class="text-xl text-gray-500 line-through">{{ product?.formatted_original_price }}</span>
                                </div>

                                <!-- Stock Status -->
                                <div v-if="product?.type === 'physical'">
                                    <div v-if="product?.stock_quantity > 0" class="flex items-center text-green-600">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="font-medium">In Stock</span>
                                        <span v-if="product.stock_quantity <= 5" class="ml-2 text-orange-600">
                                            (Only {{ product.stock_quantity }} left)
                                        </span>
                                    </div>
                                    <div v-else class="flex items-center text-red-600">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="font-medium">Out of Stock</span>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                                    <p class="text-gray-600 leading-relaxed">{{ product?.description }}</p>
                                </div>

                                <!-- Digital Product Info -->
                                <div v-if="product?.type === 'digital'" class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                    <h4 class="font-semibold text-purple-900 mb-2">Digital Product Details</h4>
                                    <ul class="text-purple-800 text-sm space-y-1">
                                        <li v-if="product.download_limit">• Download limit: {{ product.download_limit }} times</li>
                                        <li v-if="product.download_expiry_days">• Access expires: {{ product.download_expiry_days }} days after purchase</li>
                                        <li>• Instant download after payment</li>
                                        <li>• No shipping required</li>
                                    </ul>
                                </div>

                                <!-- Quantity and Add to Cart -->
                                <div class="space-y-4">
                                    <div v-if="product?.type === 'physical'" class="flex items-center space-x-4">
                                        <label class="text-sm font-medium text-gray-700">Quantity:</label>
                                        <div class="flex items-center space-x-2">
                                            <button
                                                @click="decreaseQuantity"
                                                :disabled="quantity <= 1"
                                                class="w-10 h-10 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                            >
                                                -
                                            </button>
                                            <span class="w-16 text-center font-medium text-lg">{{ quantity }}</span>
                                            <button
                                                @click="increaseQuantity"
                                                :disabled="product?.manage_stock && quantity >= product?.stock_quantity"
                                                class="w-10 h-10 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                            >
                                                +
                                            </button>
                                        </div>
                                        <div class="text-lg font-semibold text-gray-900">
                                            Total: {{ formattedTotalPrice }}
                                        </div>
                                    </div>

                                    <div class="flex space-x-4">
                                        <button
                                            @click="addToCart"
                                            :disabled="!product?.can_purchase || addingToCart"
                                            :class="[
                                                'flex-1 py-3 px-6 rounded-lg font-medium transition-colors',
                                                product?.can_purchase && !addingToCart
                                                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            ]"
                                        >
                                            <span v-if="addingToCart" class="flex items-center justify-center">
                                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                Adding...
                                            </span>
                                            <span v-else-if="product?.can_purchase">
                                                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                                                </svg>
                                                Add to Cart
                                            </span>
                                            <span v-else>Out of Stock</span>
                                        </button>

                                        <Link 
                                            href="/shop/cart"
                                            class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                                        >
                                            View Cart
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Products -->
                <div v-if="relatedProducts?.length > 0" class="mt-12">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Products</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <Link
                                    v-for="relatedProduct in relatedProducts"
                                    :key="relatedProduct.id"
                                    :href="`/shop/products/${relatedProduct.slug}`"
                                    class="group"
                                >
                                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200">
                                        <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200">
                                            <img
                                                :src="relatedProduct.primary_image || '/images/placeholder-product.jpg'"
                                                :alt="relatedProduct.name"
                                                class="h-48 w-full object-cover object-center group-hover:opacity-75"
                                            />
                                        </div>
                                        <div class="p-4">
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ relatedProduct.name }}</h3>
                                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ relatedProduct.short_description }}</p>
                                            <div class="flex items-center justify-between">
                                                <span class="text-lg font-bold text-blue-600">{{ relatedProduct.formatted_price }}</span>
                                                <span v-if="relatedProduct.type === 'digital'" class="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded">
                                                    Digital
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
