{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-lv-rLV/values-lv-rLV.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,201,269,362,432,493,566,631,697,781,851,924,985,1059,1145,1215,1280,1357,1427,1519,1599,1680,1767,1869,1950,1999,2048,2127,2189,2272,2345,2447,2531,2631", "endColumns": "63,81,67,92,69,60,72,64,65,83,69,72,60,73,85,69,64,76,69,91,79,80,86,101,80,48,48,78,61,82,72,101,83,99,95", "endOffsets": "114,196,264,357,427,488,561,626,692,776,846,919,980,1054,1140,1210,1275,1352,1422,1514,1594,1675,1762,1864,1945,1994,2043,2122,2184,2267,2340,2442,2526,2626,2722"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,109,110,120,130,131,133,140,141,150,151,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1621,1873,2150,2301,2394,2464,2977,3050,3189,3255,3339,3409,3482,3543,3617,3772,3842,4085,5034,5104,6143,6303,6384,10039,10141,11127,11830,11879,12043,12825,12908,13623,13725,14426,14526", "endColumns": "63,81,67,92,69,60,72,64,65,83,69,72,60,73,85,69,64,76,69,91,79,80,86,101,80,48,48,78,61,82,72,101,83,99,95", "endOffsets": "1680,1950,2213,2389,2459,2520,3045,3110,3250,3334,3404,3477,3538,3612,3698,3837,3902,4157,5099,5191,6218,6379,6466,10136,10217,11171,11874,11953,12100,12903,12976,13720,13804,14521,14617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,342,425,488,554,609,683,769,845,951,1057,1146,1229,1315,1404,1505,1604,1676,1772,1863,1957,2075,2167,2263,2342,2443,2526,2615,2704,2789,2868,2974,3042,3122,3229,3301,3366,4100,4784,4859,4974,5068,5123,5214,5300,5368,5456,5543,5610,5697,5746,5824,5930,6000,6092,6141,6221,6319,6366,6414,6498,6556,6627,6829,7000,7134,7201,7284,7355,7444,7525,7605,7682,7771,7845,7931,8021,8077,8203,8252,8306,8377,8449,8522,8589,8662,8751", "endColumns": "70,79,61,73,82,62,65,54,73,85,75,105,105,88,82,85,88,100,98,71,95,90,93,117,91,95,78,100,82,88,88,84,78,105,67,79,106,71,64,733,683,74,114,93,54,90,85,67,87,86,66,86,48,77,105,69,91,48,79,97,46,47,83,57,70,201,170,133,66,82,70,88,80,79,76,88,73,85,89,55,125,48,53,70,71,72,66,72,88,82", "endOffsets": "121,201,263,337,420,483,549,604,678,764,840,946,1052,1141,1224,1310,1399,1500,1599,1671,1767,1858,1952,2070,2162,2258,2337,2438,2521,2610,2699,2784,2863,2969,3037,3117,3224,3296,3361,4095,4779,4854,4969,5063,5118,5209,5295,5363,5451,5538,5605,5692,5741,5819,5925,5995,6087,6136,6216,6314,6361,6409,6493,6551,6622,6824,6995,7129,7196,7279,7350,7439,7520,7600,7677,7766,7840,7926,8016,8072,8198,8247,8301,8372,8444,8517,8584,8657,8746,8829"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,85,86,87,96,97,98,99,100,101,102,103,104,105,106,107,114,115,116,117,118,121,126,127,128,129,134,135,136,137,138,139,143,144,153,154,156,157,161,162,164,173,174,211,212,213,214,218,227,228,229,230,231,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "586,657,737,799,873,956,1019,1330,1385,1459,1545,1767,1955,2061,2218,2587,2888,3984,4237,4336,4408,4571,4662,4756,4942,5268,5364,5443,5544,5627,5716,5805,5890,5969,6075,6223,6471,6736,6808,6873,8375,9059,9134,9249,9343,9398,9489,9575,9643,9731,9818,9885,10469,10518,10596,10702,10772,11176,11557,11637,11735,11782,12105,12189,12247,12318,12520,12691,13035,13102,13940,14011,14180,14261,14622,14699,14854,15569,15655,19390,19446,19572,19621,20044,21434,21506,21579,21646,21719,23069", "endColumns": "70,79,61,73,82,62,65,54,73,85,75,105,105,88,82,85,88,100,98,71,95,90,93,117,91,95,78,100,82,88,88,84,78,105,67,79,106,71,64,733,683,74,114,93,54,90,85,67,87,86,66,86,48,77,105,69,91,48,79,97,46,47,83,57,70,201,170,133,66,82,70,88,80,79,76,88,73,85,89,55,125,48,53,70,71,72,66,72,88,82", "endOffsets": "652,732,794,868,951,1014,1080,1380,1454,1540,1616,1868,2056,2145,2296,2668,2972,4080,4331,4403,4499,4657,4751,4869,5029,5359,5438,5539,5622,5711,5800,5885,5964,6070,6138,6298,6573,6803,6868,7602,9054,9129,9244,9338,9393,9484,9570,9638,9726,9813,9880,9967,10513,10591,10697,10767,10859,11220,11632,11730,11777,11825,12184,12242,12313,12515,12686,12820,13097,13180,14006,14095,14256,14336,14694,14783,14923,15650,15740,19441,19567,19616,19670,20110,21501,21574,21641,21714,21803,23147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,210,341,405,498,557,618,698,797,972,1048,1120,1208,1295,1417,1552,1617,1693,1854,1924,1986,2059,2135,2255,2371,2438,2542,2631,2712,2792,2863", "endColumns": "74,79,130,63,92,58,60,79,98,174,75,71,87,86,121,134,64,75,160,69,61,72,75,119,115,66,103,88,80,79,70,182", "endOffsets": "125,205,336,400,493,552,613,693,792,967,1043,1115,1203,1290,1412,1547,1612,1688,1849,1919,1981,2054,2130,2250,2366,2433,2537,2626,2707,2787,2858,3041"}, "to": {"startLines": "16,18,152,168,210,221,222,223,224,225,238,239,240,241,242,243,244,245,247,248,249,250,251,252,253,254,255,256,257,258,259,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1085,1250,13809,15187,19297,20889,20948,21009,21089,21188,22348,22424,22496,22584,22671,22793,22928,22993,23152,23313,23383,23445,23518,23594,23714,23830,23897,24001,24090,24171,24251,24322", "endColumns": "74,79,130,63,92,58,60,79,98,174,75,71,87,86,121,134,64,75,160,69,61,72,75,119,115,66,103,88,80,79,70,182", "endOffsets": "1155,1325,13935,15246,19385,20943,21004,21084,21183,21358,22419,22491,22579,22666,22788,22923,22988,23064,23308,23378,23440,23513,23589,23709,23825,23892,23996,24085,24166,24246,24317,24500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,242,370,471,562,681,851,1042,1186,1278,1383,1478,1575,1672,1734,1802,1896,2005,2195,2279,2364,2436,2513,2640,2724,2818,2885,2947,3052,3155,3278,3380,3450,3521,3604,3680,3793,3918", "endColumns": "89,96,127,100,90,118,169,190,143,91,104,94,96,96,61,67,93,108,189,83,84,71,76,126,83,93,66,61,104,102,122,101,69,70,82,75,112,124,87", "endOffsets": "140,237,365,466,557,676,846,1037,1181,1273,1378,1473,1570,1667,1729,1797,1891,2000,2190,2274,2359,2431,2508,2635,2719,2813,2880,2942,3047,3150,3273,3375,3445,3516,3599,3675,3788,3913,4001"}, "to": {"startLines": "17,95,112,122,123,170,176,177,178,179,181,182,183,185,186,187,188,189,190,191,192,193,194,195,200,201,202,203,204,205,206,207,208,226,233,234,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1160,8278,10277,11225,11326,15326,15806,15976,16167,16311,16506,16611,16706,16893,16990,17052,17120,17214,17323,17513,17597,17682,17754,17831,18297,18381,18475,18542,18604,18709,18812,18935,19037,21363,21863,21946,22022,22135,22260", "endColumns": "89,96,127,100,90,118,169,190,143,91,104,94,96,96,61,67,93,108,189,83,84,71,76,126,83,93,66,61,104,102,122,101,69,70,82,75,112,124,87", "endOffsets": "1245,8370,10400,11321,11412,15440,15971,16162,16306,16398,16606,16701,16798,16985,17047,17115,17209,17318,17508,17592,17677,17749,17826,17953,18376,18470,18537,18599,18704,18807,18930,19032,19102,21429,21941,22017,22130,22255,22343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,199,263,349,414,488,557,634,709,776,844", "endColumns": "81,61,63,85,64,73,68,76,74,66,67,71", "endOffsets": "132,194,258,344,409,483,552,629,704,771,839,911"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1685,2525,2673,2737,2823,3115,3703,3907,4162,4504,4874,5196", "endColumns": "81,61,63,85,64,73,68,76,74,66,67,71", "endOffsets": "1762,2582,2732,2818,2883,3184,3767,3979,4232,4566,4937,5263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,261,329,421,493,718,789,884,951,1006,1070,1333,1407,1473,1558,1612,1692,1811,1908,1966,2050,2130,2215,2281,2386,2462,2540,2615,2676,2739,2800,2903,2993,3093,3167,3246,3332,3522,3721,3833,3891,4602,4665", "endColumns": "157,47,67,91,71,224,70,94,66,54,63,262,73,65,84,53,79,118,96,57,83,79,84,65,104,75,77,74,60,62,60,102,89,99,73,78,85,189,198,111,57,710,62,54", "endOffsets": "208,256,324,416,488,713,784,879,946,1001,1065,1328,1402,1468,1553,1607,1687,1806,1903,1961,2045,2125,2210,2276,2381,2457,2535,2610,2671,2734,2795,2898,2988,3088,3162,3241,3327,3517,3716,3828,3886,4597,4660,4715"}, "to": {"startLines": "84,88,89,90,91,92,93,94,108,111,113,119,124,125,132,142,145,146,147,148,149,155,158,163,165,166,167,169,171,172,175,180,184,196,197,198,199,209,215,216,217,219,220,232", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6578,7607,7655,7723,7815,7887,8112,8183,9972,10222,10405,10864,11417,11491,11958,12981,13185,13265,13384,13481,13539,14100,14341,14788,14928,15033,15109,15251,15445,15506,15745,16403,16803,17958,18058,18132,18211,19107,19675,19874,19986,20115,20826,21808", "endColumns": "157,47,67,91,71,224,70,94,66,54,63,262,73,65,84,53,79,118,96,57,83,79,84,65,104,75,77,74,60,62,60,102,89,99,73,78,85,189,198,111,57,710,62,54", "endOffsets": "6731,7650,7718,7810,7882,8107,8178,8273,10034,10272,10464,11122,11486,11552,12038,13030,13260,13379,13476,13534,13618,14175,14421,14849,15028,15104,15182,15321,15501,15564,15801,16501,16888,18053,18127,18206,18292,19292,19869,19981,20039,20821,20884,21858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,143,246,312,379,443,520", "endColumns": "87,102,65,66,63,76,65", "endOffsets": "138,241,307,374,438,515,581"}}]}]}