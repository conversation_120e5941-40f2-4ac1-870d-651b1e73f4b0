{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-nn-rNO/values-nn-rNO.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,244,311,378,442,514", "endColumns": "86,101,66,66,63,71,66", "endOffsets": "137,239,306,373,437,509,576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,231,350,454,543,659,835,1025,1167,1258,1367,1460,1557,1649,1708,1773,1870,1975,2171,2256,2346,2417,2489,2608,2694,2784,2851,2910,3011,3116,3246,3334,3406,3478,3564,3636,3744,3854", "endColumns": "83,91,118,103,88,115,175,189,141,90,108,92,96,91,58,64,96,104,195,84,89,70,71,118,85,89,66,58,100,104,129,87,71,71,85,71,107,109,85", "endOffsets": "134,226,345,449,538,654,830,1020,1162,1253,1362,1455,1552,1644,1703,1768,1865,1970,2166,2251,2341,2412,2484,2603,2689,2779,2846,2905,3006,3111,3241,3329,3401,3473,3559,3631,3739,3849,3935"}, "to": {"startLines": "17,95,112,122,123,170,176,177,178,179,181,182,183,185,186,187,188,189,190,191,192,193,194,195,200,201,202,203,204,205,206,207,208,226,233,234,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1127,7951,9944,10897,11001,14864,15332,15508,15698,15840,16031,16140,16233,16423,16515,16574,16639,16736,16841,17037,17122,17212,17283,17355,17812,17898,17988,18055,18114,18215,18320,18450,18538,20795,21275,21361,21433,21541,21651", "endColumns": "83,91,118,103,88,115,175,189,141,90,108,92,96,91,58,64,96,104,195,84,89,70,71,118,85,89,66,58,100,104,129,87,71,71,85,71,107,109,85", "endOffsets": "1206,8038,10058,10996,11085,14975,15503,15693,15835,15926,16135,16228,16325,16510,16569,16634,16731,16836,17032,17117,17207,17278,17350,17469,17893,17983,18050,18109,18210,18315,18445,18533,18605,20862,21356,21428,21536,21646,21732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,351,418,479,546,613,677,745,815,875,934,1007,1071,1141,1204,1279,1343,1432,1506,1591,1682,1790,1878,1926,1974,2055,2119,2191,2260,2359,2445,2537", "endColumns": "64,77,66,85,66,60,66,66,63,67,69,59,58,72,63,69,62,74,63,88,73,84,90,107,87,47,47,80,63,71,68,98,85,91,92", "endOffsets": "115,193,260,346,413,474,541,608,672,740,810,870,929,1002,1066,1136,1199,1274,1338,1427,1501,1586,1677,1785,1873,1921,1969,2050,2114,2186,2255,2354,2440,2532,2625"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,109,110,120,130,131,133,140,141,150,151,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1588,1827,2084,2233,2319,2386,2872,2939,3076,3140,3208,3278,3338,3397,3470,3593,3663,3896,4759,4823,5848,6001,6086,9693,9801,10803,11495,11543,11677,12374,12446,13199,13298,13993,14085", "endColumns": "64,77,66,85,66,60,66,66,63,67,69,59,58,72,63,69,62,74,63,88,73,84,90,107,87,47,47,80,63,71,68,98,85,91,92", "endOffsets": "1648,1900,2146,2314,2381,2442,2934,3001,3135,3203,3273,3333,3392,3465,3529,3658,3721,3966,4818,4907,5917,6081,6172,9796,9884,10846,11538,11619,11736,12441,12510,13293,13379,14080,14173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,254,339,401,471,530,606,678,743,803", "endColumns": "77,58,61,84,61,69,58,75,71,64,59,68", "endOffsets": "128,187,249,334,396,466,525,601,673,738,798,867"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1653,2447,2583,2645,2730,3006,3534,3726,3971,4289,4621,4912", "endColumns": "77,58,61,84,61,69,58,75,71,64,59,68", "endOffsets": "1726,2501,2640,2725,2787,3071,3588,3797,4038,4349,4676,4976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,260,327,429,498,748,813,907,973,1028,1092,1408,1483,1549,1602,1655,1750,1882,1996,2053,2137,2213,2296,2361,2461,2539,2619,2680,2740,2802,2862,2962,3055,3153,3226,3305,3393,3594,3789,3903,3959,4631,4693", "endColumns": "156,47,66,101,68,249,64,93,65,54,63,315,74,65,52,52,94,131,113,56,83,75,82,64,99,77,79,60,59,61,59,99,92,97,72,78,87,200,194,113,55,671,61,54", "endOffsets": "207,255,322,424,493,743,808,902,968,1023,1087,1403,1478,1544,1597,1650,1745,1877,1991,2048,2132,2208,2291,2356,2456,2534,2614,2675,2735,2797,2857,2957,3050,3148,3221,3300,3388,3589,3784,3898,3954,4626,4688,4743"}, "to": {"startLines": "84,88,89,90,91,92,93,94,108,111,113,119,124,125,132,142,145,146,147,148,149,155,158,163,165,166,167,169,171,172,175,180,184,196,197,198,199,209,215,216,217,219,220,232", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6281,7256,7304,7371,7473,7542,7792,7857,9627,9889,10063,10487,11090,11165,11624,12515,12717,12812,12944,13058,13115,13662,13910,14344,14486,14586,14664,14803,14980,15040,15272,15931,16330,17474,17572,17645,17724,18610,19182,19377,19491,19616,20288,21220", "endColumns": "156,47,66,101,68,249,64,93,65,54,63,315,74,65,52,52,94,131,113,56,83,75,82,64,99,77,79,60,59,61,59,99,92,97,72,78,87,200,194,113,55,671,61,54", "endOffsets": "6433,7299,7366,7468,7537,7787,7852,7946,9688,9939,10122,10798,11160,11226,11672,12563,12807,12939,13053,13110,13194,13733,13988,14404,14581,14659,14739,14859,15035,15097,15327,16026,16418,17567,17640,17719,17807,18806,19372,19486,19542,20283,20345,21270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,467,530,583,661,743,824,920,1016,1099,1181,1258,1338,1432,1522,1591,1678,1761,1844,1945,2023,2110,2186,2280,2359,2455,2548,2637,2721,2817,2890,2969,3073,3143,3208,3891,4550,4624,4742,4837,4897,5000,5095,5162,5252,5340,5397,5475,5524,5600,5696,5764,5835,5881,5958,6050,6097,6145,6212,6270,6330,6495,6660,6778,6844,6927,7002,7102,7187,7274,7352,7440,7517,7602,7687,7740,7872,7920,7973,8042,8108,8178,8242,8314,8395", "endColumns": "67,76,61,67,76,59,62,52,77,81,80,95,95,82,81,76,79,93,89,68,86,82,82,100,77,86,75,93,78,95,92,88,83,95,72,78,103,69,64,682,658,73,117,94,59,102,94,66,89,87,56,77,48,75,95,67,70,45,76,91,46,47,66,57,59,164,164,117,65,82,74,99,84,86,77,87,76,84,84,52,131,47,52,68,65,69,63,71,80,77", "endOffsets": "118,195,257,325,402,462,525,578,656,738,819,915,1011,1094,1176,1253,1333,1427,1517,1586,1673,1756,1839,1940,2018,2105,2181,2275,2354,2450,2543,2632,2716,2812,2885,2964,3068,3138,3203,3886,4545,4619,4737,4832,4892,4995,5090,5157,5247,5335,5392,5470,5519,5595,5691,5759,5830,5876,5953,6045,6092,6140,6207,6265,6325,6490,6655,6773,6839,6922,6997,7097,7182,7269,7347,7435,7512,7597,7682,7735,7867,7915,7968,8037,8103,8173,8237,8309,8390,8468"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,85,86,87,96,97,98,99,100,101,102,103,104,105,106,107,114,115,116,117,118,121,126,127,128,129,134,135,136,137,138,139,143,144,153,154,156,157,161,162,164,173,174,211,212,213,214,218,227,228,229,230,231,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "581,649,726,788,856,933,993,1294,1347,1425,1507,1731,1905,2001,2151,2506,2792,3802,4043,4133,4202,4354,4437,4520,4681,4981,5068,5144,5238,5317,5413,5506,5595,5679,5775,5922,6177,6438,6508,6573,8043,8702,8776,8894,8989,9049,9152,9247,9314,9404,9492,9549,10127,10176,10252,10348,10416,10851,11231,11308,11400,11447,11741,11808,11866,11926,12091,12256,12568,12634,13487,13562,13738,13823,14178,14256,14409,15102,15187,18896,18949,19081,19129,19547,20867,20933,21003,21067,21139,22428", "endColumns": "67,76,61,67,76,59,62,52,77,81,80,95,95,82,81,76,79,93,89,68,86,82,82,100,77,86,75,93,78,95,92,88,83,95,72,78,103,69,64,682,658,73,117,94,59,102,94,66,89,87,56,77,48,75,95,67,70,45,76,91,46,47,66,57,59,164,164,117,65,82,74,99,84,86,77,87,76,84,84,52,131,47,52,68,65,69,63,71,80,77", "endOffsets": "644,721,783,851,928,988,1051,1342,1420,1502,1583,1822,1996,2079,2228,2578,2867,3891,4128,4197,4284,4432,4515,4616,4754,5063,5139,5233,5312,5408,5501,5590,5674,5770,5843,5996,6276,6503,6568,7251,8697,8771,8889,8984,9044,9147,9242,9309,9399,9487,9544,9622,10171,10247,10343,10411,10482,10892,11303,11395,11442,11490,11803,11861,11921,12086,12251,12369,12629,12712,13557,13657,13818,13905,14251,14339,14481,15182,15267,18944,19076,19124,19177,19611,20928,20998,21062,21134,21215,22501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,209,312,371,456,510,568,642,742,901,975,1043,1130,1221,1327,1451,1519,1592,1760,1829,1888,1962,2044,2178,2306,2371,2485,2570,2645,2735,2803", "endColumns": "70,82,102,58,84,53,57,73,99,158,73,67,86,90,105,123,67,72,167,68,58,73,81,133,127,64,113,84,74,89,67,155", "endOffsets": "121,204,307,366,451,505,563,637,737,896,970,1038,1125,1216,1322,1446,1514,1587,1755,1824,1883,1957,2039,2173,2301,2366,2480,2565,2640,2730,2798,2954"}, "to": {"startLines": "16,18,152,168,210,221,222,223,224,225,238,239,240,241,242,243,244,245,247,248,249,250,251,252,253,254,255,256,257,258,259,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1056,1211,13384,14744,18811,20350,20404,20462,20536,20636,21737,21811,21879,21966,22057,22163,22287,22355,22506,22674,22743,22802,22876,22958,23092,23220,23285,23399,23484,23559,23649,23717", "endColumns": "70,82,102,58,84,53,57,73,99,158,73,67,86,90,105,123,67,72,167,68,58,73,81,133,127,64,113,84,74,89,67,155", "endOffsets": "1122,1289,13482,14798,18891,20399,20457,20531,20631,20790,21806,21874,21961,22052,22158,22282,22350,22423,22669,22738,22797,22871,22953,23087,23215,23280,23394,23479,23554,23644,23712,23868"}}]}]}