{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3195,3287,3386,3480,3574,3667,3760,12285", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3282,3381,3475,3569,3662,3755,3851,12381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5212", "endColumns": "107", "endOffsets": "5315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6711,6767,6823,6881,6934,7006,7060,7134,7210", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "6762,6818,6876,6929,7001,7055,7129,7205,7264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,234,292,367,431,549,604,676,732,785,849,993,1053,1113,1164,1215,1281,1368,1445,1500,1571,1638,1705,1765,1840,1907,1979,2033,2091,2149,2207,2297,2374,2464,2534,2613,2698,2799,2908,3000,3049,3285,3342", "endColumns": "134,43,57,74,63,117,54,71,55,52,63,143,59,59,50,50,65,86,76,54,70,66,66,59,74,66,71,53,57,57,57,89,76,89,69,78,84,100,108,91,48,235,56,54", "endOffsets": "185,229,287,362,426,544,599,671,727,780,844,988,1048,1108,1159,1210,1276,1363,1440,1495,1566,1633,1700,1760,1835,1902,1974,2028,2086,2144,2202,2292,2369,2459,2529,2608,2693,2794,2903,2995,3044,3280,3337,3392"}, "to": {"startLines": "239,243,244,245,246,247,248,249,263,266,268,274,279,280,287,297,300,301,302,303,304,310,313,318,320,321,322,324,326,327,330,335,339,351,352,353,354,364,370,371,372,374,375,387", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17884,18469,18513,18571,18646,18710,18828,18883,20104,20295,20426,20809,21185,21245,21640,22301,22475,22541,22628,22705,22760,23176,23366,23705,23823,23898,23965,24088,24214,24272,24476,24954,25296,26214,26304,26374,26453,27232,27613,27722,27814,27919,28155,28926", "endColumns": "134,43,57,74,63,117,54,71,55,52,63,143,59,59,50,50,65,86,76,54,70,66,66,59,74,66,71,53,57,57,57,89,76,89,69,78,84,100,108,91,48,235,56,54", "endOffsets": "18014,18508,18566,18641,18705,18823,18878,18950,20155,20343,20485,20948,21240,21300,21686,22347,22536,22623,22700,22755,22826,23238,23428,23760,23893,23960,24032,24137,24267,24325,24529,25039,25368,26299,26369,26448,26533,27328,27717,27809,27858,28150,28207,28976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba2bbf823ab5a18be680588e75f7e6ba\\transformed\\jetified-play-services-wallet-19.2.1\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "91,422", "startColumns": "4,4", "startOffsets": "7785,31568", "endColumns": "60,71", "endOffsets": "7841,31635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,428,488,536,601,667,735,819,903,976,1045,1115,1186,1266,1345,1408,1484,1557,1627,1710,1780,1856,1926,2009,2074,2149,2222,2291,2360,2438,2498,2565,2657,2719,2780,3107,3424,3489,3573,3653,3708,3784,3856,3914,3985,4058,4113,4183,4228,4298,4380,4437,4502,4546,4610,4687,4730,4773,4828,4886,4944,5035,5127,5206,5266,5329,5388,5463,5526,5586,5648,5719,5777,5846,5923,5972,6045,6090,6140,6196,6252,6313,6372,6433,6504", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,61,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,43,63,76,42,42,54,57,57,90,91,78,59,62,58,74,62,59,61,70,57,68,76,48,72,44,49,55,55,60,58,60,70,61", "endOffsets": "110,178,239,300,369,423,483,531,596,662,730,814,898,971,1040,1110,1181,1261,1340,1403,1479,1552,1622,1705,1775,1851,1921,2004,2069,2144,2217,2286,2355,2433,2493,2560,2652,2714,2775,3102,3419,3484,3568,3648,3703,3779,3851,3909,3980,4053,4108,4178,4223,4293,4375,4432,4497,4541,4605,4682,4725,4768,4823,4881,4939,5030,5122,5201,5261,5324,5383,5458,5521,5581,5643,5714,5772,5841,5918,5967,6040,6085,6135,6191,6247,6308,6367,6428,6499,6561"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,240,241,242,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,272,273,276,281,282,283,284,289,290,291,292,293,294,298,299,308,309,311,312,316,317,319,328,329,366,367,368,369,373,382,383,384,385,386,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12860,12920,12988,13049,13110,13179,13233,13479,13527,13592,13658,13857,14012,14096,14233,14558,14819,15762,15975,16054,16117,16253,16326,16396,16536,16798,16874,16944,17027,17092,17167,17240,17309,17378,17456,17589,17792,18019,18081,18142,19028,19345,19410,19494,19574,19629,19705,19777,19835,19906,19979,20034,20490,20535,20605,20687,20744,20998,21305,21369,21446,21489,21753,21808,21866,21924,22015,22107,22352,22412,23042,23101,23243,23306,23572,23634,23765,24330,24399,27396,27445,27518,27563,27863,28618,28674,28735,28794,28855,29905", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,61,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,43,63,76,42,42,54,57,57,90,91,78,59,62,58,74,62,59,61,70,57,68,76,48,72,44,49,55,55,60,58,60,70,61", "endOffsets": "12915,12983,13044,13105,13174,13228,13288,13522,13587,13653,13721,13936,14091,14164,14297,14623,14885,15837,16049,16112,16188,16321,16391,16474,16601,16869,16939,17022,17087,17162,17235,17304,17373,17451,17511,17651,17879,18076,18137,18464,19340,19405,19489,19569,19624,19700,19772,19830,19901,19974,20029,20099,20530,20600,20682,20739,20804,21037,21364,21441,21484,21527,21803,21861,21919,22010,22102,22181,22407,22470,23096,23171,23301,23361,23629,23700,23818,24394,24471,27440,27513,27558,27608,27914,28669,28730,28789,28850,28921,29962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,916,994,1053,1111,1189,1250,1307,1363,1422,1480,1534,1620,1676,1734,1788,1853,1946,2020,2098,2218,2281,2344,2421,2471,2522,2588,2652,2721,2796,2857,2928,2995,3055,3135,3198,3281,3366,3440,3505,3581,3629,3693,3769,3847,3909,3973,4036,4116,4191,4267,4343,4397,4452", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,76,49,50,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,53,54,68", "endOffsets": "242,305,366,433,502,579,669,776,849,911,989,1048,1106,1184,1245,1302,1358,1417,1475,1529,1615,1671,1729,1783,1848,1941,2015,2093,2213,2276,2339,2416,2466,2517,2583,2647,2716,2791,2852,2923,2990,3050,3130,3193,3276,3361,3435,3500,3576,3624,3688,3764,3842,3904,3968,4031,4111,4186,4262,4338,4392,4447,4516"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2858,2921,2982,3049,3118,3856,3946,4053,6649,7421,7846,8056,8114,8192,8253,8310,8366,8425,8483,8537,8623,8679,8737,8791,8856,8949,9023,9101,9221,9284,9347,9424,9474,9525,9591,9655,9724,9799,9860,9931,9998,10058,10138,10201,10284,10369,10443,10508,10584,10632,10696,10772,10850,10912,10976,11039,11119,11194,11270,11346,11400,11803", "endLines": "5,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,76,49,50,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,53,54,68", "endOffsets": "292,2916,2977,3044,3113,3190,3941,4048,4121,6706,7494,7900,8109,8187,8248,8305,8361,8420,8478,8532,8618,8674,8732,8786,8851,8944,9018,9096,9216,9279,9342,9419,9469,9520,9586,9650,9719,9794,9855,9926,9993,10053,10133,10196,10279,10364,10438,10503,10579,10627,10691,10767,10845,10907,10971,11034,11114,11189,11265,11341,11395,11450,11867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,194,272,343,415,487,605,727,822,907,989,1073,1159,1238,1295,1354,1432,1517,1638,1708,1781,1849,1912,2000,2081,2162,2226,2282,2365,2449,2548,2627,2694,2754,2819,2878,2959,3044", "endColumns": "65,72,77,70,71,71,117,121,94,84,81,83,85,78,56,58,77,84,120,69,72,67,62,87,80,80,63,55,82,83,98,78,66,59,64,58,80,84,72", "endOffsets": "116,189,267,338,410,482,600,722,817,902,984,1068,1154,1233,1290,1349,1427,1512,1633,1703,1776,1844,1907,1995,2076,2157,2221,2277,2360,2444,2543,2622,2689,2749,2814,2873,2954,3039,3112"}, "to": {"startLines": "172,250,267,277,278,325,331,332,333,334,336,337,338,340,341,342,343,344,345,346,347,348,349,350,355,356,357,358,359,360,361,362,363,381,388,389,390,391,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13352,18955,20348,21042,21113,24142,24534,24652,24774,24869,25044,25126,25210,25373,25452,25509,25568,25646,25731,25852,25922,25995,26063,26126,26538,26619,26700,26764,26820,26903,26987,27086,27165,28558,28981,29046,29105,29186,29271", "endColumns": "65,72,77,70,71,71,117,121,94,84,81,83,85,78,56,58,77,84,120,69,72,67,62,87,80,80,63,55,82,83,98,78,66,59,64,58,80,84,72", "endOffsets": "13413,19023,20421,21108,21180,24209,24647,24769,24864,24949,25121,25205,25291,25447,25504,25563,25641,25726,25847,25917,25990,26058,26121,26209,26614,26695,26759,26815,26898,26982,27081,27160,27227,28613,29041,29100,29181,29266,29339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1331,1404,1469,1540,1612,1675,1720,1766,1828,1890,1943,2005,2077,2144,2214", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209,2278"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,264,265,275,285,286,288,295,296,305,306,314,315", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13726,13941,14169,14302,14375,14440,14890,14953,15076,15136,15204,15268,15329,15387,15453,15572,15637,15842,16606,16665,17516,17656,17721,20160,20232,20953,21532,21578,21691,22186,22239,22831,22903,23433,23503", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "13781,14007,14228,14370,14435,14496,14948,15009,15131,15199,15263,15324,15382,15448,15510,15632,15690,15904,16660,16730,17584,17716,17787,20227,20290,20993,21573,21635,21748,22234,22296,22898,22965,23498,23567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "70,88,89,90", "startColumns": "4,4,4,4", "startOffsets": "6240,7499,7591,7692", "endColumns": "82,91,100,92", "endOffsets": "6318,7586,7687,7780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "125,209,274,340,400,462,524"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12386,12461,12545,12610,12676,12736,12798", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "12456,12540,12605,12671,12731,12793,12855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e71e7c08b297cb32539b89f832f53d8\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,196", "endColumns": "71,68,70", "endOffsets": "122,191,262"}, "to": {"startLines": "50,73,86", "startColumns": "4,4,4", "startOffsets": "4276,6502,7350", "endColumns": "71,68,70", "endOffsets": "4343,6566,7416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,175,247,298,361,411,462,520,589,707,775,840,903,978,1056,1140,1205,1268,1381,1446,1499,1561,1627,1712,1798,1857,1935,2006,2073,2136,2196", "endColumns": "58,60,71,50,62,49,50,57,68,117,67,64,62,74,77,83,64,62,112,64,52,61,65,84,85,58,77,70,66,62,59,93", "endOffsets": "109,170,242,293,356,406,457,515,584,702,770,835,898,973,1051,1135,1200,1263,1376,1441,1494,1556,1622,1707,1793,1852,1930,2001,2068,2131,2191,2285"}, "to": {"startLines": "171,173,307,323,365,376,377,378,379,380,393,394,395,396,397,398,399,400,402,403,404,405,406,407,408,409,410,411,412,413,414,415", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13293,13418,22970,24037,27333,28212,28262,28313,28371,28440,29344,29412,29477,29540,29615,29693,29777,29842,29967,30080,30145,30198,30260,30326,30411,30497,30556,30634,30705,30772,30835,30895", "endColumns": "58,60,71,50,62,49,50,57,68,117,67,64,62,74,77,83,64,62,112,64,52,61,65,84,85,58,77,70,66,62,59,93", "endOffsets": "13347,13474,23037,24083,27391,28257,28308,28366,28435,28553,29407,29472,29535,29610,29688,29772,29837,29900,30075,30140,30193,30255,30321,30406,30492,30551,30629,30700,30767,30830,30890,30984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "69,85,147,151,416,420,421", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6174,7269,11607,11872,30989,31413,31492", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "6235,7345,11672,11986,31152,31487,31563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be80f888898887bb71616e48bf7874ba\\transformed\\jetified-ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "48,49,71,72,74,93,94,145,146,148,149,152,153,155,417,418,419", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4126,4202,6323,6411,6571,7905,7979,11455,11533,11677,11740,11991,12064,12218,31157,31232,31297", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4197,4271,6406,6497,6644,7974,8051,11528,11602,11735,11798,12059,12134,12280,31227,31292,31408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,12139", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,12213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4348,4449,4577,4692,4794,4901,5017,5119,5320,5430,5531,5660,5775,5882,5990,6045,6102", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "4444,4572,4687,4789,4896,5012,5114,5207,5425,5526,5655,5770,5877,5985,6040,6097,6169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13786,14501,14628,14688,14761,15014,15515,15695,15909,16193,16479,16735", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "13852,14553,14683,14756,14814,15071,15567,15757,15970,16248,16531,16793"}}]}]}