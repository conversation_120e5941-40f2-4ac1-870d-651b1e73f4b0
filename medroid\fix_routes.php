<?php

/**
 * Route Fix Script for Medroid Application
 * 
 * This script fixes the missing route issue for web-api/chat/start
 * and ensures all routes are properly registered.
 */

echo "=== MEDROID ROUTE FIX SCRIPT ===\n\n";

// Function to run artisan commands
function runArtisanCommand($command) {
    echo "Running: php artisan {$command}\n";
    $output = [];
    $returnCode = 0;
    exec("php artisan {$command} 2>&1", $output, $returnCode);
    
    foreach ($output as $line) {
        echo "  {$line}\n";
    }
    
    if ($returnCode === 0) {
        echo "✓ Command completed successfully\n\n";
    } else {
        echo "❌ Command failed with code {$returnCode}\n\n";
    }
    
    return $returnCode === 0;
}

// Function to check if file exists and is readable
function checkFile($path) {
    if (!file_exists($path)) {
        echo "❌ File not found: {$path}\n";
        return false;
    }
    
    if (!is_readable($path)) {
        echo "❌ File not readable: {$path}\n";
        return false;
    }
    
    echo "✓ File exists and readable: {$path}\n";
    return true;
}

try {
    echo "1. CHECKING ENVIRONMENT\n";
    echo "=======================\n";
    
    $webRoutesPath = __DIR__ . '/routes/web.php';
    $apiRoutesPath = __DIR__ . '/routes/api.php';
    
    checkFile($webRoutesPath);
    checkFile($apiRoutesPath);
    
    echo "\n2. CLEARING ALL CACHES\n";
    echo "======================\n";
    
    // Clear all caches
    runArtisanCommand('cache:clear');
    runArtisanCommand('config:clear');
    runArtisanCommand('route:clear');
    runArtisanCommand('view:clear');
    
    // Clear compiled files
    $compiledPath = __DIR__ . '/bootstrap/cache/compiled.php';
    if (file_exists($compiledPath)) {
        unlink($compiledPath);
        echo "✓ Removed compiled.php\n";
    }
    
    $servicesPath = __DIR__ . '/bootstrap/cache/services.php';
    if (file_exists($servicesPath)) {
        unlink($servicesPath);
        echo "✓ Removed services.php\n";
    }
    
    $packagesPath = __DIR__ . '/bootstrap/cache/packages.php';
    if (file_exists($packagesPath)) {
        unlink($packagesPath);
        echo "✓ Removed packages.php\n";
    }
    
    echo "\n3. CHECKING ROUTE DEFINITIONS\n";
    echo "=============================\n";
    
    // Check web.php for the route definition
    $webContent = file_get_contents($webRoutesPath);
    
    if (strpos($webContent, "Route::prefix('web-api/chat')") !== false) {
        echo "✓ Found web-api/chat prefix in web.php\n";
        
        if (strpos($webContent, "Route::post('start'") !== false) {
            echo "✓ Found start route definition in web.php\n";
        } else {
            echo "❌ Start route definition missing in web.php\n";
        }
    } else {
        echo "❌ web-api/chat prefix missing in web.php\n";
    }
    
    echo "\n4. VALIDATING ROUTE SYNTAX\n";
    echo "==========================\n";
    
    // Check for syntax errors in route files
    $syntaxCheck = runArtisanCommand('route:list --compact');
    
    if (!$syntaxCheck) {
        echo "❌ Route syntax validation failed\n";
        echo "Please check your route files for syntax errors\n\n";
    }
    
    echo "5. CHECKING CONTROLLER\n";
    echo "======================\n";
    
    $controllerPath = __DIR__ . '/app/Http/Controllers/AIChatController.php';
    if (checkFile($controllerPath)) {
        $controllerContent = file_get_contents($controllerPath);
        
        if (strpos($controllerContent, 'function startConversation') !== false) {
            echo "✓ startConversation method found in AIChatController\n";
        } else {
            echo "❌ startConversation method missing in AIChatController\n";
        }
    }
    
    echo "\n6. CHECKING MIDDLEWARE\n";
    echo "======================\n";
    
    // Check if auth middleware is properly configured
    runArtisanCommand('route:list --name=web.api.chat.start');
    
    echo "\n7. TESTING ROUTE REGISTRATION\n";
    echo "=============================\n";
    
    // Force route registration
    runArtisanCommand('optimize:clear');
    
    echo "\n8. PRODUCTION FIXES\n";
    echo "==================\n";
    
    echo "For PRODUCTION environment, run these commands:\n\n";
    
    echo "# Clear all caches\n";
    echo "php artisan cache:clear\n";
    echo "php artisan config:clear\n";
    echo "php artisan route:clear\n";
    echo "php artisan view:clear\n";
    echo "php artisan optimize:clear\n\n";
    
    echo "# Re-optimize for production\n";
    echo "php artisan config:cache\n";
    echo "php artisan route:cache\n";
    echo "php artisan view:cache\n";
    echo "php artisan optimize\n\n";
    
    echo "# Restart web server\n";
    echo "# For Apache: sudo systemctl restart apache2\n";
    echo "# For Nginx: sudo systemctl restart nginx\n";
    echo "# For PHP-FPM: sudo systemctl restart php8.1-fpm\n\n";
    
    echo "9. VERIFICATION STEPS\n";
    echo "=====================\n";
    
    echo "After running the fixes:\n\n";
    echo "1. Check route exists:\n";
    echo "   php artisan route:list | grep 'web-api/chat/start'\n\n";
    
    echo "2. Test the endpoint:\n";
    echo "   curl -X POST https://app.medroid.ai/web-api/chat/start \\\n";
    echo "        -H 'Content-Type: application/json' \\\n";
    echo "        -H 'Accept: application/json' \\\n";
    echo "        -d '{}'\n\n";
    
    echo "3. Check Laravel logs:\n";
    echo "   tail -f storage/logs/laravel.log\n\n";
    
    echo "10. COMMON ISSUES & SOLUTIONS\n";
    echo "=============================\n";
    
    echo "Issue: Route still not found after cache clear\n";
    echo "Solution: Check if route is inside middleware group that requires authentication\n\n";
    
    echo "Issue: 500 Internal Server Error\n";
    echo "Solution: Check controller method exists and has proper return type\n\n";
    
    echo "Issue: CSRF token mismatch\n";
    echo "Solution: Ensure CSRF middleware is properly configured for API routes\n\n";
    
    echo "Issue: Authentication required\n";
    echo "Solution: Check if route requires auth middleware and user is logged in\n\n";
    
} catch (Exception $e) {
    echo "Error during fix process: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "=== FIX SCRIPT COMPLETED ===\n";
echo "\nNext steps:\n";
echo "1. Run this script: php fix_routes.php\n";
echo "2. Clear caches on production server\n";
echo "3. Test the endpoint\n";
echo "4. Check server logs if issues persist\n";
