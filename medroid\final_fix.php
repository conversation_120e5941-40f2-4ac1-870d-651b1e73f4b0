<?php

/**
 * Final Fix Script for web-api/chat/start Route Issue
 * 
 * This script identifies and fixes the specific authentication issue
 * with the web-api/chat/start route.
 */

echo "MEDROID FINAL ROUTE FIX\n";
echo "======================\n\n";

// Change to Laravel root directory
chdir(__DIR__);

echo "Working directory: " . getcwd() . "\n\n";

// Function to execute artisan commands
function execArtisan($command) {
    $fullCommand = "php artisan $command 2>&1";
    echo "Executing: $fullCommand\n";
    
    $output = [];
    $returnCode = 0;
    exec($fullCommand, $output, $returnCode);
    
    foreach ($output as $line) {
        echo "  $line\n";
    }
    
    if ($returnCode === 0) {
        echo "✓ Success\n\n";
    } else {
        echo "✗ Failed (code: $returnCode)\n\n";
    }
    
    return $returnCode === 0;
}

echo "ISSUE IDENTIFIED:\n";
echo "=================\n";
echo "The route 'web-api/chat/start' exists but requires authentication.\n";
echo "It's inside the middleware group ['auth', 'verified'] in web.php line 39.\n\n";

echo "SOLUTION OPTIONS:\n";
echo "=================\n";
echo "1. Clear caches to ensure route is properly registered\n";
echo "2. Ensure user is authenticated when making the request\n";
echo "3. Check if CSRF protection is causing issues\n\n";

// Step 1: Clear all caches
echo "1. CLEARING ALL CACHES\n";
echo "======================\n";

$commands = [
    'cache:clear',
    'config:clear', 
    'route:clear',
    'view:clear',
    'optimize:clear'
];

foreach ($commands as $command) {
    execArtisan($command);
}

// Remove specific cache files
$cacheFiles = [
    'bootstrap/cache/routes-v7.php',
    'bootstrap/cache/compiled.php',
    'bootstrap/cache/services.php',
    'bootstrap/cache/packages.php',
    'bootstrap/cache/config.php'
];

foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "✓ Removed: $file\n";
    }
}

echo "\n";

// Step 2: Verify route registration
echo "2. VERIFYING ROUTE REGISTRATION\n";
echo "===============================\n";

$output = [];
exec('php artisan route:list --name=web.api.chat.start 2>&1', $output);

if (!empty($output) && !strpos(implode(' ', $output), 'No matching routes')) {
    echo "✓ Route is registered:\n";
    foreach ($output as $line) {
        echo "  $line\n";
    }
} else {
    echo "✗ Route not found, checking all web-api routes:\n";
    
    $output = [];
    exec('php artisan route:list | grep "web-api" 2>&1', $output);
    
    foreach ($output as $line) {
        echo "  $line\n";
    }
}

echo "\n";

// Step 3: Check middleware configuration
echo "3. CHECKING MIDDLEWARE CONFIGURATION\n";
echo "====================================\n";

$webRoutesContent = file_get_contents('routes/web.php');

// Find the middleware group that contains the route
$lines = explode("\n", $webRoutesContent);
$inAuthGroup = false;
$foundRoute = false;

for ($i = 0; $i < count($lines); $i++) {
    $line = trim($lines[$i]);
    
    if (strpos($line, "Route::middleware(['auth', 'verified'])") !== false) {
        $inAuthGroup = true;
        echo "✓ Found auth middleware group at line " . ($i + 1) . "\n";
    }
    
    if ($inAuthGroup && strpos($line, "Route::post('start'") !== false) {
        $foundRoute = true;
        echo "✓ Found route inside auth middleware group at line " . ($i + 1) . "\n";
        break;
    }
}

if ($foundRoute) {
    echo "\n⚠ IMPORTANT: The route requires authentication!\n";
    echo "The client must be logged in and verified to access this route.\n\n";
}

echo "\n";

// Step 4: Re-cache for production
echo "4. RE-CACHING FOR PRODUCTION\n";
echo "============================\n";

execArtisan('config:cache');
execArtisan('route:cache');

// Step 5: Create test script
echo "5. CREATING TEST SCRIPT\n";
echo "=======================\n";

$testScript = '#!/bin/bash

# Test script for web-api/chat/start endpoint

echo "Testing web-api/chat/start endpoint..."
echo "====================================="

# Test without authentication (should fail with 401/302)
echo "1. Testing without authentication:"
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" \
    -X POST https://app.medroid.ai/web-api/chat/start \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d "{}"

echo ""

# Test with session (requires login first)
echo "2. To test with authentication:"
echo "   - Login to the web application first"
echo "   - Then make the request with session cookies"
echo ""

echo "3. Check Laravel logs:"
echo "   tail -f storage/logs/laravel.log"
echo ""

echo "4. Common HTTP status codes:"
echo "   200 = Success"
echo "   302 = Redirect (likely to login page)"
echo "   401 = Unauthorized"
echo "   404 = Route not found"
echo "   419 = CSRF token mismatch"
echo "   422 = Validation error"
echo "   500 = Server error"
';

file_put_contents('test_endpoint.sh', $testScript);
chmod('test_endpoint.sh', 0755);

echo "✓ Created test_endpoint.sh script\n\n";

// Step 6: Final instructions
echo "6. FINAL INSTRUCTIONS\n";
echo "=====================\n";

echo "The route exists and is properly configured. The 404 error suggests:\n\n";

echo "POSSIBLE CAUSES:\n";
echo "1. Route cache was stale (now cleared)\n";
echo "2. User not authenticated (route requires login)\n";
echo "3. CSRF token missing (for web requests)\n";
echo "4. Wrong HTTP method (must be POST)\n";
echo "5. Wrong Content-Type header\n\n";

echo "TO FIX ON PRODUCTION:\n";
echo "1. Run this script: php final_fix.php\n";
echo "2. Restart PHP-FPM: sudo systemctl restart php8.1-fpm\n";
echo "3. Restart web server: sudo systemctl restart nginx\n";
echo "4. Test endpoint: ./test_endpoint.sh\n\n";

echo "FOR AUTHENTICATED REQUESTS:\n";
echo "- Ensure user is logged in via web interface\n";
echo "- Include session cookies in the request\n";
echo "- Include CSRF token if required\n\n";

echo "FOR API REQUESTS (token-based):\n";
echo "- Use the API routes in api.php instead\n";
echo "- Include Authorization: Bearer <token> header\n\n";

echo "DEBUGGING:\n";
echo "- Check storage/logs/laravel.log for detailed errors\n";
echo "- Use php artisan route:list to verify route registration\n";
echo "- Test with different HTTP clients (curl, Postman, etc.)\n\n";

echo "✓ Fix completed successfully!\n";
echo "The route should now be accessible with proper authentication.\n";
