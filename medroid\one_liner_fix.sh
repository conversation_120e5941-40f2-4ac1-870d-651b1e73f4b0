#!/bin/bash

# One-liner fix for the route issue
echo "🚀 MEDROID ROUTE FIX - One Liner"
echo "================================"

# Clear all caches and restart services
php artisan cache:clear && \
php artisan config:clear && \
php artisan route:clear && \
php artisan view:clear && \
php artisan optimize:clear && \
php artisan config:cache && \
php artisan route:cache && \
echo "✅ Caches cleared and regenerated" && \

# Test route registration
echo "🔍 Testing route registration..." && \
php artisan route:list | grep -q "web-api/chat/start" && \
echo "✅ Route is registered" || echo "❌ Route not found" && \

# Restart services (if possible)
(sudo systemctl restart php8.1-fpm 2>/dev/null || sudo systemctl restart php8.0-fpm 2>/dev/null || echo "⚠️  Could not restart PHP-FPM") && \
(sudo systemctl reload nginx 2>/dev/null || sudo systemctl reload apache2 2>/dev/null || echo "⚠️  Could not reload web server") && \

echo "🎉 Fix completed! Test the endpoint now."
