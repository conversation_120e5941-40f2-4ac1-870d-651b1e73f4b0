<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import BulkImportModal from '@/components/BulkImportModal.vue';

// Get user from page props
const page = usePage();
const user = computed(() => page.props.auth?.user);

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Products', href: '/admin/products' },
];

const loading = ref(false);
const products = ref([]);
const categories = ref([]);
const searchQuery = ref('');
const selectedCategory = ref('all');
const selectedType = ref('all');
const showBulkImportModal = ref(false);

const fetchProducts = async () => {
    loading.value = true;
    try {
        const params = new URLSearchParams();
        if (searchQuery.value) params.append('search', searchQuery.value);
        if (selectedCategory.value !== 'all') params.append('category', selectedCategory.value);
        if (selectedType.value !== 'all') params.append('type', selectedType.value);
        
        const response = await window.axios.get(`/admin/products-list?${params.toString()}`);
        products.value = response.data.products?.data || response.data.products || [];
        if (response.data.categories) {
            categories.value = response.data.categories;
        }
    } catch (error) {
        console.error('Error fetching products:', error);
        products.value = [];
    } finally {
        loading.value = false;
    }
};

const getStatusBadgeClass = (isActive) => {
    return isActive 
        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
};

const getTypeBadgeClass = (type) => {
    return type === 'digital'
        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
        : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
};

const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
};

const filteredProducts = computed(() => {
    return products.value.filter(product => {
        const matchesSearch = !searchQuery.value ||
            product.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            product.sku.toLowerCase().includes(searchQuery.value.toLowerCase());

        const matchesCategory = selectedCategory.value === 'all' ||
            product.category_id == selectedCategory.value;

        const matchesType = selectedType.value === 'all' ||
            product.type === selectedType.value;

        return matchesSearch && matchesCategory && matchesType;
    });
});

// Permission checks
const isAdmin = computed(() => user.value?.roles?.some(role => role.name === 'admin') || false);
const canCreateProducts = computed(() => isAdmin.value || user.value?.user_permissions?.includes('create products') || false);
const canEditProducts = computed(() => isAdmin.value || user.value?.user_permissions?.includes('edit products') || false);
const canDeleteProducts = computed(() => isAdmin.value || user.value?.user_permissions?.includes('delete products') || false);

// Check if user can edit/delete specific product
const canEditProduct = (product) => {
    if (isAdmin.value) return true;
    return canEditProducts.value && product.user_id === user.value?.id;
};

const canDeleteProduct = (product) => {
    if (isAdmin.value) return true;
    return canDeleteProducts.value && product.user_id === user.value?.id;
};

const handleBulkImportSuccess = (result) => {
    alert(`Successfully imported ${result.imported_count} products!`);
    fetchProducts();
};

onMounted(() => {
    fetchProducts();
});
</script>

<template>
    <Head title="Product Management" />

    <AppLayout :breadcrumbs="breadcrumbs">

        <div class="py-6">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Page Header with Actions -->
                <div class="mb-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-xl font-semibold leading-tight text-gray-800">
                                    Product Management
                                </h2>
                                <p class="text-gray-600 text-sm mt-1">Manage your products and inventory</p>
                            </div>

                            <!-- Action Buttons -->
                            <div v-if="canCreateProducts" class="flex space-x-3">
                                <button
                                    @click="showBulkImportModal = true"
                                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    <i class="fas fa-upload mr-2"></i>
                                    Bulk Import
                                </button>
                                <Link href="/admin/products/create" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Product
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Filters -->
                <div class="mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <input 
                                    v-model="searchQuery"
                                    type="text" 
                                    placeholder="Search products..." 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                            </div>
                            <div>
                                <select 
                                    v-model="selectedCategory"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                                    <option value="all">All Categories</option>
                                    <option v-for="category in categories" :key="category.id" :value="category.id">
                                        {{ category.name }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <select 
                                    v-model="selectedType"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                                    <option value="all">All Types</option>
                                    <option value="physical">Physical</option>
                                    <option value="digital">Digital</option>
                                </select>
                            </div>
                            <div>
                                <button 
                                    @click="fetchProducts"
                                    class="w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="filteredProducts.length === 0" class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">No products found.</p>
                        </div>

                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Product
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Category
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Type
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Price
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="product in filteredProducts" :key="product.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img
                                                        v-if="product.primary_image"
                                                        :src="`/storage/${product.primary_image}`"
                                                        :alt="product.name"
                                                        class="h-10 w-10 rounded object-cover"
                                                    >
                                                    <div v-else class="h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                        <i class="fas fa-box text-gray-500 dark:text-gray-400"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ product.name }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        SKU: {{ product.sku }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ product.category?.name || 'N/A' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getTypeBadgeClass(product.type)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ product.type }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ formatPrice(product.price) }}
                                                <span v-if="product.sale_price" class="text-xs text-gray-500 line-through ml-1">
                                                    {{ formatPrice(product.sale_price) }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusBadgeClass(product.is_active)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ product.is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <Link :href="`/admin/products/${product.id}`" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                                                View
                                            </Link>
                                            <Link v-if="canEditProduct(product)" :href="`/admin/products/${product.id}/edit`" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">
                                                Edit
                                            </Link>
                                            <button v-if="canDeleteProduct(product)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Import Modal -->
        <BulkImportModal
            :is-open="showBulkImportModal"
            @close="showBulkImportModal = false"
            @imported="handleBulkImportSuccess"
        />
    </AppLayout>
</template>
