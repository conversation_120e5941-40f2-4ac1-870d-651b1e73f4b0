<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add admin fields to chats table
        Schema::table('chats', function (Blueprint $table) {
            $table->unsignedBigInteger('flagged_by')->nullable()->after('flag_reason');
            $table->timestamp('flagged_at')->nullable()->after('flagged_by');
            $table->unsignedBigInteger('archived_by')->nullable()->after('is_archived');
            $table->timestamp('archived_at')->nullable()->after('archived_by');
            
            $table->foreign('flagged_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('archived_by')->references('id')->on('users')->onDelete('set null');
        });

        // Add admin fields to chat_messages table
        Schema::table('chat_messages', function (Blueprint $table) {
            $table->boolean('hidden')->default(false)->after('is_system_message');
            $table->unsignedBigInteger('hidden_by')->nullable()->after('hidden');
            $table->timestamp('hidden_at')->nullable()->after('hidden_by');
            $table->unsignedBigInteger('edited_by')->nullable()->after('hidden_at');
            $table->timestamp('edited_at')->nullable()->after('edited_by');
            
            $table->foreign('hidden_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('edited_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('chats', function (Blueprint $table) {
            $table->dropForeign(['flagged_by']);
            $table->dropForeign(['archived_by']);
            $table->dropColumn(['flagged_by', 'flagged_at', 'archived_by', 'archived_at']);
        });

        Schema::table('chat_messages', function (Blueprint $table) {
            $table->dropForeign(['hidden_by']);
            $table->dropForeign(['edited_by']);
            $table->dropColumn(['hidden', 'hidden_by', 'hidden_at', 'edited_by', 'edited_at']);
        });
    }
};
