import{r as c,o as h,d as a,e as r,f as y,u as _,m as w,g as k,i as s,l as P,v as D,x as i,s as L,t as n,F as v,q as M,j as g,a as N}from"./vendor-CKE3WRFf.js";import{_ as V}from"./AppLayout.vue_vue_type_script_setup_true_lang-Dvt6ywSf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B8nQAqQg.js";import"./createLucideIcon-Dy3o-9bT.js";const S={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},j={class:"mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},C={class:"flex-1 max-w-md"},B={class:"relative"},U=["disabled"],E={class:"bg-white rounded-lg shadow-sm border"},F={class:"p-6"},R={key:0,class:"text-center py-8"},T={key:1,class:"text-center py-8"},q={key:2,class:"space-y-4"},z={class:"flex justify-between items-start"},Q={class:"flex items-center space-x-4"},$={class:"font-medium text-gray-900"},A={class:"text-sm text-gray-600"},G={class:"flex items-center space-x-4 mt-1 text-xs text-gray-500"},H={key:0},I={key:1},J={class:"flex flex-col items-end space-y-2"},K={class:"text-sm text-gray-600"},ts={__name:"Patients",setup(O){const b=[{title:"Dashboard",href:"/dashboard"},{title:"My Patients",href:"/provider/patients"}],l=c(!1),d=c([]),u=c(""),f=async()=>{l.value=!0;try{const o=await N.get("/provider/get-patients");d.value=o.data.patients||[]}catch(o){console.error("Error fetching patients:",o)}finally{l.value=!1}},m=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return h(()=>{f()}),(o,t)=>(r(),a(v,null,[y(_(w),{title:"My Patients"}),y(V,{breadcrumbs:b},{default:k(()=>[s("div",S,[t[9]||(t[9]=s("div",{class:"mb-8"},[s("h1",{class:"text-3xl font-bold text-gray-900"},"My Patients"),s("p",{class:"mt-2 text-gray-600"},"Manage your patient records and history")],-1)),s("div",j,[s("div",C,[s("div",B,[t[1]||(t[1]=s("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},null,-1)),P(s("input",{"onUpdate:modelValue":t[0]||(t[0]=e=>u.value=e),type:"text",placeholder:"Search patients...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[D,u.value]])])]),s("button",{onClick:f,disabled:l.value,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"},[s("i",{class:L(["fas",l.value?"fa-spinner fa-spin":"fa-sync-alt","mr-2"])},null,2),i(" "+n(l.value?"Loading...":"Refresh"),1)],8,U)]),s("div",E,[t[8]||(t[8]=s("div",{class:"p-6 border-b"},[s("h2",{class:"text-xl font-semibold text-gray-900"},"Patient List")],-1)),s("div",F,[l.value?(r(),a("div",R,t[2]||(t[2]=[s("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"},null,-1),s("p",{class:"text-gray-600"},"Loading patients...",-1)]))):d.value.length===0?(r(),a("div",T,t[3]||(t[3]=[s("i",{class:"fas fa-users text-4xl text-gray-300 mb-4"},null,-1),s("p",{class:"text-gray-600"},"No patients found",-1),s("p",{class:"text-sm text-gray-500 mt-2"},"Patients will appear here after their first appointment",-1)]))):(r(),a("div",q,[(r(!0),a(v,null,M(d.value,e=>{var p,x;return r(),a("div",{key:e.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[s("div",z,[s("div",Q,[t[6]||(t[6]=s("div",{class:"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center"},[s("i",{class:"fas fa-user text-gray-500"})],-1)),s("div",null,[s("h3",$,n(((p=e.user)==null?void 0:p.name)||"Unknown Patient"),1),s("p",A,n((x=e.user)==null?void 0:x.email),1),s("div",G,[e.date_of_birth?(r(),a("span",H,[t[4]||(t[4]=s("i",{class:"fas fa-birthday-cake mr-1"},null,-1)),i(" "+n(m(e.date_of_birth)),1)])):g("",!0),e.phone?(r(),a("span",I,[t[5]||(t[5]=s("i",{class:"fas fa-phone mr-1"},null,-1)),i(" "+n(e.phone),1)])):g("",!0)])])]),s("div",J,[s("div",K," Last visit: "+n(e.last_appointment?m(e.last_appointment):"Never"),1),t[7]||(t[7]=s("div",{class:"flex space-x-2"},[s("button",{class:"text-blue-600 hover:text-blue-800 text-sm"},[s("i",{class:"fas fa-eye mr-1"}),i(" View Records ")]),s("button",{class:"text-green-600 hover:text-green-800 text-sm"},[s("i",{class:"fas fa-calendar-plus mr-1"}),i(" Schedule ")])],-1))])])])}),128))]))])])])]),_:1})],64))}};export{ts as default};
