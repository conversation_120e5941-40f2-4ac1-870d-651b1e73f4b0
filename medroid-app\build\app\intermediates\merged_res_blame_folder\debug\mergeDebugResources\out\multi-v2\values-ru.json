{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,249,391,495,570,689,867,1080,1230,1321,1419,1514,1610,1702,1764,1832,1925,2029,2244,2328,2413,2487,2564,2712,2804,2902,2971,3034,3138,3249,3383,3476,3547,3620,3717,3790,3920,4038", "endColumns": "89,103,141,103,74,118,177,212,149,90,97,94,95,91,61,67,92,103,214,83,84,73,76,147,91,97,68,62,103,110,133,92,70,72,96,72,129,117,95", "endOffsets": "140,244,386,490,565,684,862,1075,1225,1316,1414,1509,1605,1697,1759,1827,1920,2024,2239,2323,2408,2482,2559,2707,2799,2897,2966,3029,3133,3244,3378,3471,3542,3615,3712,3785,3915,4033,4129"}, "to": {"startLines": "174,252,269,279,280,327,333,334,335,336,338,339,340,342,343,344,345,346,347,348,349,350,351,352,357,358,359,360,361,362,363,364,365,383,390,391,392,393,394", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15460,22623,24653,25665,25769,29799,30284,30462,30675,30825,31023,31121,31216,31413,31505,31567,31635,31728,31832,32047,32131,32216,32290,32367,32893,32985,33083,33152,33215,33319,33430,33564,33657,36088,36590,36687,36760,36890,37008", "endColumns": "89,103,141,103,74,118,177,212,149,90,97,94,95,91,61,67,92,103,214,83,84,73,76,147,91,97,68,62,103,110,133,92,70,72,96,72,129,117,95", "endOffsets": "15545,22722,24790,25764,25839,29913,30457,30670,30820,30911,31116,31211,31307,31500,31562,31630,31723,31827,32042,32126,32211,32285,32362,32510,32980,33078,33147,33210,33314,33425,33559,33652,33723,36156,36682,36755,36885,37003,37099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,198,265,349,418,479,551,615,680,766,836,908,971,1045,1109,1182,1246,1334,1399,1485,1561,1651,1747,1861,1942,1993,2044,2130,2194,2266,2340,2456,2550,2651", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "113,193,260,344,413,474,546,610,675,761,831,903,966,1040,1104,1177,1241,1329,1394,1480,1556,1646,1742,1856,1937,1988,2039,2125,2189,2261,2335,2451,2545,2646,2744"}, "to": {"startLines": "180,183,186,188,189,190,197,198,200,201,202,203,204,205,206,208,209,212,223,224,236,238,239,266,267,277,287,288,290,297,298,307,308,316,317", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15916,16164,16437,16586,16670,16739,17249,17321,17457,17522,17608,17678,17750,17813,17887,18009,18082,18325,19276,19341,20414,20561,20651,24404,24518,25565,26258,26309,26448,27190,27262,28001,28117,28844,28945", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "15974,16239,16499,16665,16734,16795,17316,17380,17517,17603,17673,17745,17808,17882,17946,18077,18141,18408,19336,19422,20485,20646,20742,24513,24594,25611,26304,26390,26507,27257,27331,28112,28206,28940,29038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be80f888898887bb71616e48bf7874ba\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "50,51,73,74,76,95,96,147,148,150,151,154,155,157,419,420,421", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4630,4723,7338,7436,7619,9173,9255,13267,13355,13514,13585,13880,13964,14133,39480,39564,39634", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "4718,4801,7431,7533,7706,9250,9340,13350,13432,13580,13650,13959,14046,14200,39559,39629,39752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "72,90,91,92", "startColumns": "4,4,4,4", "startOffsets": "7230,8713,8818,8930", "endColumns": "107,104,111,104", "endOffsets": "7333,8813,8925,9030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7777,7849,7910,7975,8041,8119,8193,8281,8367", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "7844,7905,7970,8036,8114,8188,8276,8362,8439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,354,450,515,586,641,712,801,875,980,1085,1173,1255,1340,1430,1532,1634,1708,1807,1900,1990,2105,2190,2289,2370,2476,2550,2643,2748,2842,2936,3037,3103,3174,3290,3361,3425,4168,4845,4919,5025,5132,5187,5276,5362,5431,5518,5620,5676,5766,5815,5892,5999,6064,6135,6184,6264,6356,6403,6457,6531,6589,6668,6847,7002,7135,7202,7296,7375,7472,7555,7636,7712,7801,7883,7970,8064,8120,8257,8307,8362,8451,8518,8587,8657,8726,8814", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,48,79,91,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,86,93,55,136,49,54,88,66,68,69,68,87,73", "endOffsets": "119,197,267,349,445,510,581,636,707,796,870,975,1080,1168,1250,1335,1425,1527,1629,1703,1802,1895,1985,2100,2185,2284,2365,2471,2545,2638,2743,2837,2931,3032,3098,3169,3285,3356,3420,4163,4840,4914,5020,5127,5182,5271,5357,5426,5513,5615,5671,5761,5810,5887,5994,6059,6130,6179,6259,6351,6398,6452,6526,6584,6663,6842,6997,7130,7197,7291,7370,7467,7550,7631,7707,7796,7878,7965,8059,8115,8252,8302,8357,8446,8513,8582,8652,8721,8809,8883"}, "to": {"startLines": "166,167,168,169,170,171,172,176,177,178,179,182,184,185,187,192,196,211,214,215,216,218,219,220,222,226,227,228,229,230,231,232,233,234,235,237,240,242,243,244,253,254,255,256,257,258,259,260,261,262,263,264,271,272,273,274,275,278,283,284,285,286,291,292,293,294,295,296,300,301,310,311,313,314,318,319,321,330,331,368,369,370,371,375,384,385,386,387,388,403", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14852,14921,14999,15069,15151,15247,15312,15627,15682,15753,15842,16059,16244,16349,16504,16860,17159,18223,18490,18592,18666,18833,18926,19016,19191,19501,19600,19681,19787,19861,19954,20059,20153,20247,20348,20490,20747,21038,21109,21173,22727,23404,23478,23584,23691,23746,23835,23921,23990,24077,24179,24235,24859,24908,24985,25092,25157,25616,25985,26065,26157,26204,26512,26586,26644,26723,26902,27057,27389,27456,28338,28417,28596,28679,29043,29119,29293,30043,30130,34007,34063,34200,34250,34710,36161,36228,36297,36367,36436,37846", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,48,79,91,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,86,93,55,136,49,54,88,66,68,69,68,87,73", "endOffsets": "14916,14994,15064,15146,15242,15307,15378,15677,15748,15837,15911,16159,16344,16432,16581,16940,17244,18320,18587,18661,18760,18921,19011,19126,19271,19595,19676,19782,19856,19949,20054,20148,20242,20343,20409,20556,20858,21104,21168,21911,23399,23473,23579,23686,23741,23830,23916,23985,24072,24174,24230,24320,24903,24980,25087,25152,25223,25660,26060,26152,26199,26253,26581,26639,26718,26897,27052,27185,27451,27545,28412,28509,28674,28755,29114,29203,29370,30125,30219,34058,34195,34245,34300,34794,36223,36292,36362,36431,36519,37915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,209,336,393,485,544,607,709,817,1003,1097,1169,1248,1338,1469,1604,1669,1745,1931,1998,2061,2133,2216,2333,2457,2525,2634,2723,2812,2900,2979", "endColumns": "76,76,126,56,91,58,62,101,107,185,93,71,78,89,130,134,64,75,185,66,62,71,82,116,123,67,108,88,88,87,78,156", "endOffsets": "127,204,331,388,480,539,602,704,812,998,1092,1164,1243,1333,1464,1599,1664,1740,1926,1993,2056,2128,2211,2328,2452,2520,2629,2718,2807,2895,2974,3131"}, "to": {"startLines": "173,175,309,325,367,378,379,380,381,382,395,396,397,398,399,400,401,402,404,405,406,407,408,409,410,411,412,413,414,415,416,417", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15383,15550,28211,29660,33915,35570,35629,35692,35794,35902,37104,37198,37270,37349,37439,37570,37705,37770,37920,38106,38173,38236,38308,38391,38508,38632,38700,38809,38898,38987,39075,39154", "endColumns": "76,76,126,56,91,58,62,101,107,185,93,71,78,89,130,134,64,75,185,66,62,71,82,116,123,67,108,88,88,87,78,156", "endOffsets": "15455,15622,28333,29712,34002,35624,35687,35789,35897,36083,37193,37265,37344,37434,37565,37700,37765,37841,38101,38168,38231,38303,38386,38503,38627,38695,38804,38893,38982,39070,39149,39306"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,14051", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,14128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,276,343,442,517,785,845,937,1016,1070,1134,1471,1544,1612,1665,1718,1797,1912,2023,2090,2169,2251,2335,2420,2539,2628,2705,2787,2847,2912,2972,3079,3180,3309,3383,3462,3558,3745,3956,4087,4150,4857,4921", "endColumns": "174,45,66,98,74,267,59,91,78,53,63,336,72,67,52,52,78,114,110,66,78,81,83,84,118,88,76,81,59,64,59,106,100,128,73,78,95,186,210,130,62,706,63,65", "endOffsets": "225,271,338,437,512,780,840,932,1011,1065,1129,1466,1539,1607,1660,1713,1792,1907,2018,2085,2164,2246,2330,2415,2534,2623,2700,2782,2842,2907,2967,3074,3175,3304,3378,3457,3553,3740,3951,4082,4145,4852,4916,4982"}, "to": {"startLines": "241,245,246,247,248,249,250,251,265,268,270,276,281,282,289,299,302,303,304,305,306,312,315,320,322,323,324,326,328,329,332,337,341,353,354,355,356,366,372,373,374,376,377,389", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20863,21916,21962,22029,22128,22203,22471,22531,24325,24599,24795,25228,25844,25917,26395,27336,27550,27629,27744,27855,27922,28514,28760,29208,29375,29494,29583,29717,29918,29978,30224,30916,31312,32515,32644,32718,32797,33728,34305,34516,34647,34799,35506,36524", "endColumns": "174,45,66,98,74,267,59,91,78,53,63,336,72,67,52,52,78,114,110,66,78,81,83,84,118,88,76,81,59,64,59,106,100,128,73,78,95,186,210,130,62,706,63,65", "endOffsets": "21033,21957,22024,22123,22198,22466,22526,22618,24399,24648,24854,25560,25912,25980,26443,27384,27624,27739,27850,27917,27996,28591,28839,29288,29489,29578,29655,29794,29973,30038,30279,31018,31408,32639,32713,32792,32888,33910,34511,34642,34705,35501,35565,36585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3577,3675,3777,3878,3979,4084,4187,14205", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3670,3772,3873,3974,4079,4182,4299,14301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "71,87,149,153,418,422,423", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7156,8444,13437,13735,39311,39757,39839", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "7225,8531,13509,13875,39475,39834,39912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4883,4990,5156,5282,5392,5534,5663,5778,6039,6220,6327,6490,6616,6783,6941,7010,7070", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "4985,5151,5277,5387,5529,5658,5773,5877,6215,6322,6485,6611,6778,6936,7005,7065,7151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,195,259,344,409,481,539,616,693,761,821", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "130,190,254,339,404,476,534,611,688,756,816,890"}, "to": {"startLines": "181,191,193,194,195,199,207,210,213,217,221,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15979,16800,16945,17009,17094,17385,17951,18146,18413,18765,19131,19427", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "16054,16855,17004,17089,17154,17452,18004,18218,18485,18828,19186,19496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e71e7c08b297cb32539b89f832f53d8\\transformed\\jetified-material3-1.0.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "52,75,88", "startColumns": "4,4,4", "startOffsets": "4806,7538,8536", "endColumns": "76,80,77", "endOffsets": "4878,7614,8609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5882", "endColumns": "156", "endOffsets": "6034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,244,311,378,442,529", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "136,239,306,373,437,524,596"}, "to": {"startLines": "159,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14306,14392,14495,14562,14629,14693,14780", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "14387,14490,14557,14624,14688,14775,14847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,2989,3043,3094,3160,3232,3310,3406,3486,3562,3639,3716,3805,3878,3968,4063,4137,4218,4311,4366,4432,4518,4603,4665,4729,4792,4890,4990,5085,5187,5245,5300", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,90,53,50,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,2984,3038,3089,3155,3227,3305,3401,3481,3557,3634,3711,3800,3873,3963,4058,4132,4213,4306,4361,4427,4513,4598,4660,4724,4787,4885,4985,5080,5182,5240,5295,5375"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,89,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3388,3486,4304,4401,4538,7711,8614,9096,9345,9408,9526,9587,9652,9709,9779,9840,9894,10010,10067,10129,10183,10257,10385,10473,10559,10696,10780,10865,10956,11010,11061,11127,11199,11277,11373,11453,11529,11606,11683,11772,11845,11935,12030,12104,12185,12278,12333,12399,12485,12570,12632,12696,12759,12857,12957,13052,13154,13212,13655", "endLines": "7,35,36,37,38,39,47,48,49,77,89,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,152", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,90,53,50,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,57,54,79", "endOffsets": "426,3221,3299,3383,3481,3572,4396,4533,4625,7772,8708,9168,9403,9521,9582,9647,9704,9774,9835,9889,10005,10062,10124,10178,10252,10380,10468,10554,10691,10775,10860,10951,11005,11056,11122,11194,11272,11368,11448,11524,11601,11678,11767,11840,11930,12025,12099,12180,12273,12328,12394,12480,12565,12627,12691,12754,12852,12952,13047,13149,13207,13262,13730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba2bbf823ab5a18be680588e75f7e6ba\\transformed\\jetified-play-services-wallet-19.2.1\\res\\values-ru\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "93,424", "startColumns": "4,4", "startOffsets": "9035,39917", "endColumns": "60,78", "endOffsets": "9091,39991"}}]}]}