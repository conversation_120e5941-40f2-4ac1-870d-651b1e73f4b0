import 'package:medroid_app/models/chat_recommendation.dart';

enum MessageContentType {
  text,
  image,
  audio,
  video,
  appointment, // Special type for appointment confirmations
  appointmentSlots, // Special type for displaying appointment slots
}

class ChatMessage {
  final String id;
  final String role;
  final String content;
  final DateTime timestamp;
  final bool isTruncated;
  final MessageContentType contentType;
  final String? imageUrl;
  final String? localImagePath;
  final List<dynamic>? appointmentSlots;

  ChatMessage({
    required this.id,
    required this.role,
    required this.content,
    required this.timestamp,
    this.isTruncated = false,
    this.contentType = MessageContentType.text,
    this.imageUrl,
    this.localImagePath,
    this.appointmentSlots,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    MessageContentType type = MessageContentType.text;
    if (json['content_type'] != null) {
      switch (json['content_type'].toString().toLowerCase()) {
        case 'image':
          type = MessageContentType.image;
          break;
        case 'audio':
          type = MessageContentType.audio;
          break;
        case 'video':
          type = MessageContentType.video;
          break;
        default:
          type = MessageContentType.text;
      }
    }

    return ChatMessage(
      id: json['_id'] != null
          ? json['_id'].toString()
          : DateTime.now().millisecondsSinceEpoch.toString(),
      role: json['role'] ?? 'user',
      content: json['content'] ?? '',
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
      isTruncated: json['is_truncated'] ?? false,
      contentType: type,
      imageUrl: json['image_url'],
      localImagePath: json['local_image_path'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      '_id': id,
      'role': role,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'is_truncated': isTruncated,
      'content_type': contentType.toString().split('.').last,
    };

    if (imageUrl != null) {
      data['image_url'] = imageUrl;
    }

    if (localImagePath != null) {
      data['local_image_path'] = localImagePath;
    }

    return data;
  }

  bool get isUser => role == 'user';
  bool get isAssistant => role == 'assistant';
  bool get isImage => contentType == MessageContentType.image;
  bool get isText => contentType == MessageContentType.text;
  bool get isAppointment => contentType == MessageContentType.appointment;
  bool get isAppointmentSlots =>
      contentType == MessageContentType.appointmentSlots;

  String get formattedTime {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }
}

class ChatConversation {
  final String id;
  final String patientId;
  final String title;
  final List<ChatMessage> messages;
  final List<String> healthConcerns;
  final List<ChatRecommendation> recommendations;
  final bool escalated;
  final DateTime createdAt;
  final DateTime updatedAt;

  ChatConversation({
    required this.id,
    required this.patientId,
    this.title = 'New Conversation',
    required this.messages,
    required this.healthConcerns,
    required this.recommendations,
    required this.escalated,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ChatConversation.fromJson(Map<String, dynamic> json) {
    // Get messages to help generate a title if one doesn't exist
    final messages = (json['messages'] as List<dynamic>?)
            ?.map((m) => ChatMessage.fromJson(m))
            .toList() ??
        [];

    // Generate a default title from the first user message if no title exists
    String defaultTitle = 'New Conversation';
    if (messages.isNotEmpty) {
      final firstUserMsg = messages.firstWhere(
        (m) => m.isUser,
        orElse: () => messages.first,
      );
      if (firstUserMsg.content.isNotEmpty) {
        defaultTitle = firstUserMsg.content.length > 30
            ? '${firstUserMsg.content.substring(0, 27)}...'
            : firstUserMsg.content;
      }
    }

    return ChatConversation(
      id: json['_id'] != null ? json['_id'].toString() : '',
      patientId:
          json['patient_id'] != null ? json['patient_id'].toString() : '',
      title: json['title'] ?? defaultTitle,
      messages: messages,
      healthConcerns: List<String>.from(json['health_concerns'] ?? []),
      recommendations: (json['recommendations'] as List<dynamic>?)
              ?.map((r) => ChatRecommendation.fromJson(r))
              .toList() ??
          [],
      escalated: json['escalated'] ?? false,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'patient_id': patientId,
      'title': title,
      'messages': messages.map((m) => m.toJson()).toList(),
      'health_concerns': healthConcerns,
      'recommendations': recommendations.map((r) => r.toJson()).toList(),
      'escalated': escalated,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get lastMessage {
    if (messages.isEmpty) {
      return 'No messages';
    }
    return messages.last.content;
  }

  String get formattedDate {
    return '${updatedAt.day}/${updatedAt.month}/${updatedAt.year} ${updatedAt.hour.toString().padLeft(2, '0')}:${updatedAt.minute.toString().padLeft(2, '0')}';
  }
}
