<template>
    <div v-if="isOpen" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Bulk Import Products</h3>
                    <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Import Steps -->
                <div class="mb-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium', 
                                     step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600']">
                            1
                        </div>
                        <span :class="['text-sm', step >= 1 ? 'text-blue-600 font-medium' : 'text-gray-500']">
                            Download Template
                        </span>
                        <div class="flex-1 h-px bg-gray-200"></div>
                        <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium', 
                                     step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600']">
                            2
                        </div>
                        <span :class="['text-sm', step >= 2 ? 'text-blue-600 font-medium' : 'text-gray-500']">
                            Upload File
                        </span>
                        <div class="flex-1 h-px bg-gray-200"></div>
                        <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium', 
                                     step >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600']">
                            3
                        </div>
                        <span :class="['text-sm', step >= 3 ? 'text-blue-600 font-medium' : 'text-gray-500']">
                            Review & Import
                        </span>
                    </div>
                </div>

                <!-- Step 1: Download Template -->
                <div v-if="step === 1" class="space-y-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-medium text-blue-900 mb-2">Step 1: Download Import Template</h4>
                        <p class="text-blue-800 text-sm mb-4">
                            Download the CSV template file and fill it with your product data. Make sure to follow the format exactly.
                        </p>
                        <button
                            @click="downloadTemplate"
                            :disabled="downloading"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50"
                        >
                            <i class="fas fa-download mr-2"></i>
                            {{ downloading ? 'Downloading...' : 'Download Template' }}
                        </button>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h5 class="font-medium text-yellow-900 mb-2">Important Notes:</h5>
                        <ul class="text-yellow-800 text-sm space-y-1">
                            <li>• All required fields must be filled</li>
                            <li>• SKU must be unique for each product</li>
                            <li>• Price should be in decimal format (e.g., 29.99)</li>
                            <li>• Type should be either 'physical' or 'digital'</li>
                            <li>• Category ID must exist in the system</li>
                        </ul>
                    </div>

                    <div class="flex justify-end">
                        <button
                            @click="step = 2"
                            class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
                        >
                            Next: Upload File
                        </button>
                    </div>
                </div>

                <!-- Step 2: Upload File -->
                <div v-if="step === 2" class="space-y-4">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 class="font-medium text-green-900 mb-2">Step 2: Upload Your CSV File</h4>
                        <p class="text-green-800 text-sm mb-4">
                            Select the CSV file you've prepared with your product data.
                        </p>
                        
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <input
                                ref="fileInput"
                                type="file"
                                accept=".csv,.xlsx,.xls"
                                @change="handleFileSelect"
                                class="hidden"
                            />
                            <div v-if="!selectedFile">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600 mb-2">Click to select a file or drag and drop</p>
                                <p class="text-gray-500 text-sm">CSV, XLSX, or XLS files only</p>
                                <button
                                    @click="$refs.fileInput.click()"
                                    class="mt-4 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
                                >
                                    Select File
                                </button>
                            </div>
                            <div v-else class="space-y-2">
                                <i class="fas fa-file-csv text-4xl text-green-500"></i>
                                <p class="font-medium text-gray-900">{{ selectedFile.name }}</p>
                                <p class="text-gray-500 text-sm">{{ formatFileSize(selectedFile.size) }}</p>
                                <button
                                    @click="removeFile"
                                    class="text-red-600 hover:text-red-800 text-sm"
                                >
                                    Remove file
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <button
                            @click="step = 1"
                            class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-400"
                        >
                            Previous
                        </button>
                        <button
                            @click="validateFile"
                            :disabled="!selectedFile || validating"
                            class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50"
                        >
                            {{ validating ? 'Validating...' : 'Next: Review' }}
                        </button>
                    </div>
                </div>

                <!-- Step 3: Review & Import -->
                <div v-if="step === 3" class="space-y-4">
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <h4 class="font-medium text-purple-900 mb-2">Step 3: Review & Import</h4>
                        <p class="text-purple-800 text-sm mb-4">
                            Review the validation results and proceed with the import.
                        </p>
                        
                        <div v-if="validationResults" class="space-y-3">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Total rows:</span>
                                <span class="font-medium">{{ validationResults.total_rows }}</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-green-600">Valid rows:</span>
                                <span class="font-medium text-green-600">{{ validationResults.valid_rows }}</span>
                            </div>
                            <div v-if="validationResults.invalid_rows > 0" class="flex items-center justify-between text-sm">
                                <span class="text-red-600">Invalid rows:</span>
                                <span class="font-medium text-red-600">{{ validationResults.invalid_rows }}</span>
                            </div>
                        </div>

                        <div v-if="validationResults?.errors?.length > 0" class="mt-4">
                            <h5 class="font-medium text-red-900 mb-2">Validation Errors:</h5>
                            <div class="max-h-32 overflow-y-auto bg-red-50 border border-red-200 rounded p-3">
                                <div v-for="error in validationResults.errors.slice(0, 10)" :key="error.row" class="text-red-800 text-sm">
                                    Row {{ error.row }}: {{ error.message }}
                                </div>
                                <div v-if="validationResults.errors.length > 10" class="text-red-600 text-sm mt-2">
                                    ... and {{ validationResults.errors.length - 10 }} more errors
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <button
                            @click="step = 2"
                            class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-400"
                        >
                            Previous
                        </button>
                        <button
                            @click="importProducts"
                            :disabled="!validationResults || validationResults.valid_rows === 0 || importing"
                            class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 disabled:opacity-50"
                        >
                            {{ importing ? 'Importing...' : `Import ${validationResults?.valid_rows || 0} Products` }}
                        </button>
                    </div>
                </div>

                <!-- Import Progress -->
                <div v-if="importing" class="mt-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                            <span class="text-blue-800 font-medium">Importing products...</span>
                        </div>
                        <div v-if="importProgress" class="mt-3">
                            <div class="w-full bg-blue-200 rounded-full h-2">
                                <div 
                                    class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                    :style="{ width: `${importProgress}%` }"
                                ></div>
                            </div>
                            <p class="text-blue-700 text-sm mt-1">{{ importProgress }}% complete</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, defineEmits } from 'vue';

const emit = defineEmits(['close', 'imported']);

const step = ref(1);
const downloading = ref(false);
const selectedFile = ref(null);
const validating = ref(false);
const validationResults = ref(null);
const importing = ref(false);
const importProgress = ref(0);

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    }
});

// Detect if we're in provider or admin context
const isProviderContext = computed(() => {
    return window.location.pathname.includes('/provider/');
});

const getApiPrefix = () => {
    return isProviderContext.value ? '/provider' : '/admin';
};

const downloadTemplate = async () => {
    downloading.value = true;
    try {
        const response = await window.axios.get(`${getApiPrefix()}/products/import-template`, {
            responseType: 'blob'
        });
        
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'product_import_template.csv');
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Error downloading template:', error);
        alert('Error downloading template. Please try again.');
    } finally {
        downloading.value = false;
    }
};

const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
        selectedFile.value = file;
    }
};

const removeFile = () => {
    selectedFile.value = null;
    validationResults.value = null;
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const validateFile = async () => {
    if (!selectedFile.value) return;
    
    validating.value = true;
    try {
        const formData = new FormData();
        formData.append('file', selectedFile.value);
        
        const response = await window.axios.post(`${getApiPrefix()}/products/validate-import`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        
        validationResults.value = response.data;
        step.value = 3;
    } catch (error) {
        console.error('Error validating file:', error);
        alert('Error validating file. Please check the format and try again.');
    } finally {
        validating.value = false;
    }
};

const importProducts = async () => {
    if (!selectedFile.value || !validationResults.value) return;
    
    importing.value = true;
    importProgress.value = 0;
    
    try {
        const formData = new FormData();
        formData.append('file', selectedFile.value);
        
        const response = await window.axios.post(`${getApiPrefix()}/products/bulk-import`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
                importProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            }
        });
        
        emit('imported', response.data);
        emit('close');
    } catch (error) {
        console.error('Error importing products:', error);
        alert('Error importing products. Please try again.');
    } finally {
        importing.value = false;
        importProgress.value = 0;
    }
};
</script>
