  Activity android.app  
PendingIntent android.app  equals android.app.Activity  
startActivity android.app.Activity  startActivityForResult android.app.Activity  FLAG_MUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getBroadcast android.app.PendingIntent  getINTENTSender android.app.PendingIntent  getIntentSender android.app.PendingIntent  intentSender android.app.PendingIntent  setIntentSender android.app.PendingIntent  
ACTIVITY_CODE android.content  
AtomicBoolean android.content  BroadcastReceiver android.content  Build android.content  
ComponentName android.content  Context android.content  Intent android.content  IntentSender android.content  RESULT_UNAVAILABLE android.content  SharePlusPendingIntent android.content  java android.content  result android.content  Build !android.content.BroadcastReceiver  
ComponentName !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  result !android.content.BroadcastReceiver  equals android.content.ComponentName  flattenToString android.content.ComponentName  cacheDir android.content.Context  getCACHEDir android.content.Context  getCacheDir android.content.Context  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  grantUriPermission android.content.Context  packageManager android.content.Context  packageName android.content.Context  setCacheDir android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  
startActivity android.content.Context  startActivityForResult android.content.Context  
startActivity android.content.ContextWrapper  startActivityForResult android.content.ContextWrapper  ACTION_SEND android.content.Intent  ACTION_SEND_MULTIPLE android.content.Intent  EXTRA_CHOSEN_COMPONENT android.content.Intent  EXTRA_STREAM android.content.Intent  
EXTRA_SUBJECT android.content.Intent  
EXTRA_TEXT android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  FLAG_GRANT_WRITE_URI_PERMISSION android.content.Intent  Intent android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  
createChooser android.content.Intent  first android.content.Intent  	getACTION android.content.Intent  getAPPLY android.content.Intent  	getAction android.content.Intent  getApply android.content.Intent  getFIRST android.content.Intent  getFirst android.content.Intent  getParcelableExtra android.content.Intent  getREDUCEMimeTypes android.content.Intent  getReduceMimeTypes android.content.Intent  getTYPE android.content.Intent  getType android.content.Intent  putExtra android.content.Intent  putParcelableArrayListExtra android.content.Intent  reduceMimeTypes android.content.Intent  	setAction android.content.Intent  setType android.content.Intent  type android.content.Intent  PackageManager android.content.pm  ResolveInfo android.content.pm  packageName android.content.pm.ActivityInfo  MATCH_DEFAULT_ONLY !android.content.pm.PackageManager  queryIntentActivities !android.content.pm.PackageManager  activityInfo android.content.pm.ResolveInfo  Uri android.net  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  LOLLIPOP_MR1 android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  
startActivity  android.view.ContextThemeWrapper  startActivityForResult  android.view.ContextThemeWrapper  FileProvider androidx.core.content  
getUriForFile "androidx.core.content.FileProvider  
ACTIVITY_CODE dev.fluttercommunity.plus.share  Any dev.fluttercommunity.plus.share  	ArrayList dev.fluttercommunity.plus.share  
AtomicBoolean dev.fluttercommunity.plus.share  Boolean dev.fluttercommunity.plus.share  BroadcastReceiver dev.fluttercommunity.plus.share  Build dev.fluttercommunity.plus.share  CHANNEL dev.fluttercommunity.plus.share  
ComponentName dev.fluttercommunity.plus.share  Context dev.fluttercommunity.plus.share  File dev.fluttercommunity.plus.share  FileProvider dev.fluttercommunity.plus.share  IOException dev.fluttercommunity.plus.share  IllegalArgumentException dev.fluttercommunity.plus.share  Int dev.fluttercommunity.plus.share  Intent dev.fluttercommunity.plus.share  List dev.fluttercommunity.plus.share  Map dev.fluttercommunity.plus.share  MethodCallHandler dev.fluttercommunity.plus.share  
MethodChannel dev.fluttercommunity.plus.share  PackageManager dev.fluttercommunity.plus.share  
PendingIntent dev.fluttercommunity.plus.share  RESULT_UNAVAILABLE dev.fluttercommunity.plus.share  Share dev.fluttercommunity.plus.share  ShareFileProvider dev.fluttercommunity.plus.share  SharePlusPendingIntent dev.fluttercommunity.plus.share  SharePlusPlugin dev.fluttercommunity.plus.share  ShareSuccessManager dev.fluttercommunity.plus.share  String dev.fluttercommunity.plus.share  Throws dev.fluttercommunity.plus.share  apply dev.fluttercommunity.plus.share  contains dev.fluttercommunity.plus.share  copyTo dev.fluttercommunity.plus.share  endsWith dev.fluttercommunity.plus.share  first dev.fluttercommunity.plus.share  forEach dev.fluttercommunity.plus.share  getValue dev.fluttercommunity.plus.share  indexOf dev.fluttercommunity.plus.share  invoke dev.fluttercommunity.plus.share  
isNullOrBlank dev.fluttercommunity.plus.share  
isNullOrEmpty dev.fluttercommunity.plus.share  java dev.fluttercommunity.plus.share  	lastIndex dev.fluttercommunity.plus.share  lazy dev.fluttercommunity.plus.share  provideDelegate dev.fluttercommunity.plus.share  reduceMimeTypes dev.fluttercommunity.plus.share  require dev.fluttercommunity.plus.share  result dev.fluttercommunity.plus.share  
startsWith dev.fluttercommunity.plus.share  	substring dev.fluttercommunity.plus.share  Any 1dev.fluttercommunity.plus.share.MethodCallHandler  Build 1dev.fluttercommunity.plus.share.MethodCallHandler  IOException 1dev.fluttercommunity.plus.share.MethodCallHandler  IllegalArgumentException 1dev.fluttercommunity.plus.share.MethodCallHandler  List 1dev.fluttercommunity.plus.share.MethodCallHandler  Map 1dev.fluttercommunity.plus.share.MethodCallHandler  
MethodCall 1dev.fluttercommunity.plus.share.MethodCallHandler  
MethodChannel 1dev.fluttercommunity.plus.share.MethodCallHandler  Share 1dev.fluttercommunity.plus.share.MethodCallHandler  ShareSuccessManager 1dev.fluttercommunity.plus.share.MethodCallHandler  String 1dev.fluttercommunity.plus.share.MethodCallHandler  Throws 1dev.fluttercommunity.plus.share.MethodCallHandler  endsWith 1dev.fluttercommunity.plus.share.MethodCallHandler  expectMapArguments 1dev.fluttercommunity.plus.share.MethodCallHandler  getENDSWith 1dev.fluttercommunity.plus.share.MethodCallHandler  getEndsWith 1dev.fluttercommunity.plus.share.MethodCallHandler  
getREQUIRE 1dev.fluttercommunity.plus.share.MethodCallHandler  
getRequire 1dev.fluttercommunity.plus.share.MethodCallHandler  manager 1dev.fluttercommunity.plus.share.MethodCallHandler  require 1dev.fluttercommunity.plus.share.MethodCallHandler  share 1dev.fluttercommunity.plus.share.MethodCallHandler  Activity %dev.fluttercommunity.plus.share.Share  	ArrayList %dev.fluttercommunity.plus.share.Share  Boolean %dev.fluttercommunity.plus.share.Share  Build %dev.fluttercommunity.plus.share.Share  Context %dev.fluttercommunity.plus.share.Share  File %dev.fluttercommunity.plus.share.Share  FileProvider %dev.fluttercommunity.plus.share.Share  IOException %dev.fluttercommunity.plus.share.Share  Int %dev.fluttercommunity.plus.share.Share  Intent %dev.fluttercommunity.plus.share.Share  List %dev.fluttercommunity.plus.share.Share  PackageManager %dev.fluttercommunity.plus.share.Share  
PendingIntent %dev.fluttercommunity.plus.share.Share  SharePlusPendingIntent %dev.fluttercommunity.plus.share.Share  ShareSuccessManager %dev.fluttercommunity.plus.share.Share  String %dev.fluttercommunity.plus.share.Share  Throws %dev.fluttercommunity.plus.share.Share  Uri %dev.fluttercommunity.plus.share.Share  activity %dev.fluttercommunity.plus.share.Share  apply %dev.fluttercommunity.plus.share.Share  clearShareCacheFolder %dev.fluttercommunity.plus.share.Share  contains %dev.fluttercommunity.plus.share.Share  context %dev.fluttercommunity.plus.share.Share  copyTo %dev.fluttercommunity.plus.share.Share  copyToShareCacheFolder %dev.fluttercommunity.plus.share.Share  fileIsInShareCache %dev.fluttercommunity.plus.share.Share  first %dev.fluttercommunity.plus.share.Share  forEach %dev.fluttercommunity.plus.share.Share  getAPPLY %dev.fluttercommunity.plus.share.Share  getApply %dev.fluttercommunity.plus.share.Share  getCONTAINS %dev.fluttercommunity.plus.share.Share  	getCOPYTo %dev.fluttercommunity.plus.share.Share  getContains %dev.fluttercommunity.plus.share.Share  
getContext %dev.fluttercommunity.plus.share.Share  	getCopyTo %dev.fluttercommunity.plus.share.Share  getFIRST %dev.fluttercommunity.plus.share.Share  
getFOREach %dev.fluttercommunity.plus.share.Share  getFirst %dev.fluttercommunity.plus.share.Share  
getForEach %dev.fluttercommunity.plus.share.Share  getGETValue %dev.fluttercommunity.plus.share.Share  getGetValue %dev.fluttercommunity.plus.share.Share  
getINDEXOf %dev.fluttercommunity.plus.share.Share  getISNullOrBlank %dev.fluttercommunity.plus.share.Share  getISNullOrEmpty %dev.fluttercommunity.plus.share.Share  
getIndexOf %dev.fluttercommunity.plus.share.Share  getIsNullOrBlank %dev.fluttercommunity.plus.share.Share  getIsNullOrEmpty %dev.fluttercommunity.plus.share.Share  getLAZY %dev.fluttercommunity.plus.share.Share  getLazy %dev.fluttercommunity.plus.share.Share  getMimeTypeBase %dev.fluttercommunity.plus.share.Share  getPROVIDEDelegate %dev.fluttercommunity.plus.share.Share  getProvideDelegate %dev.fluttercommunity.plus.share.Share  
getSTARTSWith %dev.fluttercommunity.plus.share.Share  getSUBSTRING %dev.fluttercommunity.plus.share.Share  
getStartsWith %dev.fluttercommunity.plus.share.Share  getSubstring %dev.fluttercommunity.plus.share.Share  getUrisForPaths %dev.fluttercommunity.plus.share.Share  getValue %dev.fluttercommunity.plus.share.Share  immutabilityIntentFlags %dev.fluttercommunity.plus.share.Share  indexOf %dev.fluttercommunity.plus.share.Share  
isNullOrBlank %dev.fluttercommunity.plus.share.Share  
isNullOrEmpty %dev.fluttercommunity.plus.share.Share  java %dev.fluttercommunity.plus.share.Share  	lastIndex %dev.fluttercommunity.plus.share.Share  lazy %dev.fluttercommunity.plus.share.Share  manager %dev.fluttercommunity.plus.share.Share  provideDelegate %dev.fluttercommunity.plus.share.Share  providerAuthority %dev.fluttercommunity.plus.share.Share  reduceMimeTypes %dev.fluttercommunity.plus.share.Share  setActivity %dev.fluttercommunity.plus.share.Share  share %dev.fluttercommunity.plus.share.Share  shareCacheFolder %dev.fluttercommunity.plus.share.Share  
shareFiles %dev.fluttercommunity.plus.share.Share  
startActivity %dev.fluttercommunity.plus.share.Share  
startsWith %dev.fluttercommunity.plus.share.Share  	substring %dev.fluttercommunity.plus.share.Share  Build 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  	Companion 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  
ComponentName 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  Context 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  Intent 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  String 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  	getRESULT 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  	getResult 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  java 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  result 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  Build @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  
ComponentName @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  Context @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  Intent @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  String @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  java @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  result @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  ActivityPluginBinding /dev.fluttercommunity.plus.share.SharePlusPlugin  CHANNEL /dev.fluttercommunity.plus.share.SharePlusPlugin  FlutterPluginBinding /dev.fluttercommunity.plus.share.SharePlusPlugin  MethodCallHandler /dev.fluttercommunity.plus.share.SharePlusPlugin  
MethodChannel /dev.fluttercommunity.plus.share.SharePlusPlugin  Share /dev.fluttercommunity.plus.share.SharePlusPlugin  ShareSuccessManager /dev.fluttercommunity.plus.share.SharePlusPlugin  invoke /dev.fluttercommunity.plus.share.SharePlusPlugin  manager /dev.fluttercommunity.plus.share.SharePlusPlugin  
methodChannel /dev.fluttercommunity.plus.share.SharePlusPlugin  onAttachedToActivity /dev.fluttercommunity.plus.share.SharePlusPlugin  onDetachedFromActivity /dev.fluttercommunity.plus.share.SharePlusPlugin  share /dev.fluttercommunity.plus.share.SharePlusPlugin  ActivityPluginBinding 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  CHANNEL 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  FlutterPluginBinding 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  MethodCallHandler 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  
MethodChannel 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  Share 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  ShareSuccessManager 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  invoke 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  
ACTIVITY_CODE 3dev.fluttercommunity.plus.share.ShareSuccessManager  
AtomicBoolean 3dev.fluttercommunity.plus.share.ShareSuccessManager  Boolean 3dev.fluttercommunity.plus.share.ShareSuccessManager  Context 3dev.fluttercommunity.plus.share.ShareSuccessManager  Int 3dev.fluttercommunity.plus.share.ShareSuccessManager  Intent 3dev.fluttercommunity.plus.share.ShareSuccessManager  
MethodChannel 3dev.fluttercommunity.plus.share.ShareSuccessManager  RESULT_UNAVAILABLE 3dev.fluttercommunity.plus.share.ShareSuccessManager  SharePlusPendingIntent 3dev.fluttercommunity.plus.share.ShareSuccessManager  String 3dev.fluttercommunity.plus.share.ShareSuccessManager  callback 3dev.fluttercommunity.plus.share.ShareSuccessManager  isCalledBack 3dev.fluttercommunity.plus.share.ShareSuccessManager  returnResult 3dev.fluttercommunity.plus.share.ShareSuccessManager  setCallback 3dev.fluttercommunity.plus.share.ShareSuccessManager  unavailable 3dev.fluttercommunity.plus.share.ShareSuccessManager  
ACTIVITY_CODE =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  
AtomicBoolean =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  Boolean =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  Context =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  Int =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  Intent =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  
MethodChannel =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  RESULT_UNAVAILABLE =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  SharePlusPendingIntent =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  String =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  invoke =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getACTIVITY Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  setActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  equals -io.flutter.plugin.common.MethodChannel.Result  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry  File java.io  IOException java.io  
canonicalPath java.io.File  copyTo java.io.File  delete java.io.File  exists java.io.File  getCANONICALPath java.io.File  	getCOPYTo java.io.File  getCanonicalPath java.io.File  	getCopyTo java.io.File  getNAME java.io.File  getName java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  setCanonicalPath java.io.File  setName java.io.File  message java.io.IOException  
ACTIVITY_CODE 	java.lang  	ArrayList 	java.lang  
AtomicBoolean 	java.lang  Build 	java.lang  CHANNEL 	java.lang  Class 	java.lang  
ComponentName 	java.lang  File 	java.lang  FileProvider 	java.lang  IOException 	java.lang  IllegalArgumentException 	java.lang  Intent 	java.lang  MethodCallHandler 	java.lang  
MethodChannel 	java.lang  PackageManager 	java.lang  
PendingIntent 	java.lang  RESULT_UNAVAILABLE 	java.lang  Share 	java.lang  SharePlusPendingIntent 	java.lang  ShareSuccessManager 	java.lang  apply 	java.lang  contains 	java.lang  copyTo 	java.lang  endsWith 	java.lang  first 	java.lang  forEach 	java.lang  getValue 	java.lang  indexOf 	java.lang  
isNullOrBlank 	java.lang  
isNullOrEmpty 	java.lang  java 	java.lang  	lastIndex 	java.lang  lazy 	java.lang  provideDelegate 	java.lang  reduceMimeTypes 	java.lang  require 	java.lang  result 	java.lang  
startsWith 	java.lang  	substring 	java.lang  	ArrayList 	java.util  add java.util.AbstractCollection  first java.util.AbstractCollection  isEmpty java.util.AbstractCollection  add java.util.AbstractList  first java.util.AbstractList  isEmpty java.util.AbstractList  add java.util.ArrayList  first java.util.ArrayList  getFIRST java.util.ArrayList  getFirst java.util.ArrayList  isEmpty java.util.ArrayList  size java.util.ArrayList  
AtomicBoolean java.util.concurrent.atomic  
compareAndSet )java.util.concurrent.atomic.AtomicBoolean  set )java.util.concurrent.atomic.AtomicBoolean  
ACTIVITY_CODE kotlin  Any kotlin  Array kotlin  	ArrayList kotlin  
AtomicBoolean kotlin  Boolean kotlin  Build kotlin  CHANNEL kotlin  
ComponentName kotlin  File kotlin  FileProvider kotlin  	Function0 kotlin  	Function1 kotlin  IOException kotlin  IllegalArgumentException kotlin  Int kotlin  Intent kotlin  Lazy kotlin  MethodCallHandler kotlin  
MethodChannel kotlin  Nothing kotlin  PackageManager kotlin  
PendingIntent kotlin  RESULT_UNAVAILABLE kotlin  Share kotlin  SharePlusPendingIntent kotlin  ShareSuccessManager kotlin  String kotlin  Throws kotlin  apply kotlin  contains kotlin  copyTo kotlin  endsWith kotlin  first kotlin  forEach kotlin  getValue kotlin  indexOf kotlin  
isNullOrBlank kotlin  
isNullOrEmpty kotlin  java kotlin  	lastIndex kotlin  lazy kotlin  provideDelegate kotlin  reduceMimeTypes kotlin  require kotlin  result kotlin  
startsWith kotlin  	substring kotlin  
getFOREach kotlin.Array  
getForEach kotlin.Array  getISNullOrEmpty kotlin.Array  getIsNullOrEmpty kotlin.Array  
isNullOrEmpty kotlin.Array  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getCONTAINS 
kotlin.String  getContains 
kotlin.String  getENDSWith 
kotlin.String  getEndsWith 
kotlin.String  
getINDEXOf 
kotlin.String  getISNullOrBlank 
kotlin.String  
getIndexOf 
kotlin.String  getIsNullOrBlank 
kotlin.String  
getSTARTSWith 
kotlin.String  getSUBSTRING 
kotlin.String  
getStartsWith 
kotlin.String  getSubstring 
kotlin.String  
isNullOrBlank 
kotlin.String  
ACTIVITY_CODE kotlin.annotation  	ArrayList kotlin.annotation  
AtomicBoolean kotlin.annotation  Build kotlin.annotation  CHANNEL kotlin.annotation  
ComponentName kotlin.annotation  File kotlin.annotation  FileProvider kotlin.annotation  IOException kotlin.annotation  IllegalArgumentException kotlin.annotation  Intent kotlin.annotation  MethodCallHandler kotlin.annotation  
MethodChannel kotlin.annotation  PackageManager kotlin.annotation  
PendingIntent kotlin.annotation  RESULT_UNAVAILABLE kotlin.annotation  Share kotlin.annotation  SharePlusPendingIntent kotlin.annotation  ShareSuccessManager kotlin.annotation  Throws kotlin.annotation  apply kotlin.annotation  contains kotlin.annotation  copyTo kotlin.annotation  endsWith kotlin.annotation  first kotlin.annotation  forEach kotlin.annotation  getValue kotlin.annotation  indexOf kotlin.annotation  
isNullOrBlank kotlin.annotation  
isNullOrEmpty kotlin.annotation  java kotlin.annotation  	lastIndex kotlin.annotation  lazy kotlin.annotation  provideDelegate kotlin.annotation  reduceMimeTypes kotlin.annotation  require kotlin.annotation  result kotlin.annotation  
startsWith kotlin.annotation  	substring kotlin.annotation  
ACTIVITY_CODE kotlin.collections  	ArrayList kotlin.collections  
AtomicBoolean kotlin.collections  Build kotlin.collections  CHANNEL kotlin.collections  
ComponentName kotlin.collections  File kotlin.collections  FileProvider kotlin.collections  IOException kotlin.collections  IllegalArgumentException kotlin.collections  Intent kotlin.collections  List kotlin.collections  Map kotlin.collections  MethodCallHandler kotlin.collections  
MethodChannel kotlin.collections  MutableList kotlin.collections  PackageManager kotlin.collections  
PendingIntent kotlin.collections  RESULT_UNAVAILABLE kotlin.collections  Share kotlin.collections  SharePlusPendingIntent kotlin.collections  ShareSuccessManager kotlin.collections  Throws kotlin.collections  apply kotlin.collections  contains kotlin.collections  copyTo kotlin.collections  endsWith kotlin.collections  first kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  indexOf kotlin.collections  
isNullOrBlank kotlin.collections  
isNullOrEmpty kotlin.collections  java kotlin.collections  	lastIndex kotlin.collections  lazy kotlin.collections  provideDelegate kotlin.collections  reduceMimeTypes kotlin.collections  require kotlin.collections  result kotlin.collections  
startsWith kotlin.collections  	substring kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getFIRST kotlin.collections.List  getFirst kotlin.collections.List  getISNullOrEmpty kotlin.collections.List  getIsNullOrEmpty kotlin.collections.List  getLASTIndex kotlin.collections.List  getLastIndex kotlin.collections.List  
isNullOrEmpty kotlin.collections.List  
ACTIVITY_CODE kotlin.comparisons  	ArrayList kotlin.comparisons  
AtomicBoolean kotlin.comparisons  Build kotlin.comparisons  CHANNEL kotlin.comparisons  
ComponentName kotlin.comparisons  File kotlin.comparisons  FileProvider kotlin.comparisons  IOException kotlin.comparisons  IllegalArgumentException kotlin.comparisons  Intent kotlin.comparisons  MethodCallHandler kotlin.comparisons  
MethodChannel kotlin.comparisons  PackageManager kotlin.comparisons  
PendingIntent kotlin.comparisons  RESULT_UNAVAILABLE kotlin.comparisons  Share kotlin.comparisons  SharePlusPendingIntent kotlin.comparisons  ShareSuccessManager kotlin.comparisons  Throws kotlin.comparisons  apply kotlin.comparisons  contains kotlin.comparisons  copyTo kotlin.comparisons  endsWith kotlin.comparisons  first kotlin.comparisons  forEach kotlin.comparisons  getValue kotlin.comparisons  indexOf kotlin.comparisons  
isNullOrBlank kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  java kotlin.comparisons  	lastIndex kotlin.comparisons  lazy kotlin.comparisons  provideDelegate kotlin.comparisons  reduceMimeTypes kotlin.comparisons  require kotlin.comparisons  result kotlin.comparisons  
startsWith kotlin.comparisons  	substring kotlin.comparisons  
ACTIVITY_CODE 	kotlin.io  	ArrayList 	kotlin.io  
AtomicBoolean 	kotlin.io  Build 	kotlin.io  CHANNEL 	kotlin.io  
ComponentName 	kotlin.io  File 	kotlin.io  FileProvider 	kotlin.io  IOException 	kotlin.io  IllegalArgumentException 	kotlin.io  Intent 	kotlin.io  MethodCallHandler 	kotlin.io  
MethodChannel 	kotlin.io  PackageManager 	kotlin.io  
PendingIntent 	kotlin.io  RESULT_UNAVAILABLE 	kotlin.io  Share 	kotlin.io  SharePlusPendingIntent 	kotlin.io  ShareSuccessManager 	kotlin.io  Throws 	kotlin.io  apply 	kotlin.io  contains 	kotlin.io  copyTo 	kotlin.io  endsWith 	kotlin.io  first 	kotlin.io  forEach 	kotlin.io  getValue 	kotlin.io  indexOf 	kotlin.io  
isNullOrBlank 	kotlin.io  
isNullOrEmpty 	kotlin.io  java 	kotlin.io  	lastIndex 	kotlin.io  lazy 	kotlin.io  provideDelegate 	kotlin.io  reduceMimeTypes 	kotlin.io  require 	kotlin.io  result 	kotlin.io  
startsWith 	kotlin.io  	substring 	kotlin.io  
ACTIVITY_CODE 
kotlin.jvm  	ArrayList 
kotlin.jvm  
AtomicBoolean 
kotlin.jvm  Build 
kotlin.jvm  CHANNEL 
kotlin.jvm  
ComponentName 
kotlin.jvm  File 
kotlin.jvm  FileProvider 
kotlin.jvm  IOException 
kotlin.jvm  IllegalArgumentException 
kotlin.jvm  Intent 
kotlin.jvm  MethodCallHandler 
kotlin.jvm  
MethodChannel 
kotlin.jvm  PackageManager 
kotlin.jvm  
PendingIntent 
kotlin.jvm  RESULT_UNAVAILABLE 
kotlin.jvm  Share 
kotlin.jvm  SharePlusPendingIntent 
kotlin.jvm  ShareSuccessManager 
kotlin.jvm  Throws 
kotlin.jvm  apply 
kotlin.jvm  contains 
kotlin.jvm  copyTo 
kotlin.jvm  endsWith 
kotlin.jvm  first 
kotlin.jvm  forEach 
kotlin.jvm  getValue 
kotlin.jvm  indexOf 
kotlin.jvm  
isNullOrBlank 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  java 
kotlin.jvm  	lastIndex 
kotlin.jvm  lazy 
kotlin.jvm  provideDelegate 
kotlin.jvm  reduceMimeTypes 
kotlin.jvm  require 
kotlin.jvm  result 
kotlin.jvm  
startsWith 
kotlin.jvm  	substring 
kotlin.jvm  
ACTIVITY_CODE 
kotlin.ranges  	ArrayList 
kotlin.ranges  
AtomicBoolean 
kotlin.ranges  Build 
kotlin.ranges  CHANNEL 
kotlin.ranges  
ComponentName 
kotlin.ranges  File 
kotlin.ranges  FileProvider 
kotlin.ranges  IOException 
kotlin.ranges  IllegalArgumentException 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  MethodCallHandler 
kotlin.ranges  
MethodChannel 
kotlin.ranges  PackageManager 
kotlin.ranges  
PendingIntent 
kotlin.ranges  RESULT_UNAVAILABLE 
kotlin.ranges  Share 
kotlin.ranges  SharePlusPendingIntent 
kotlin.ranges  ShareSuccessManager 
kotlin.ranges  Throws 
kotlin.ranges  apply 
kotlin.ranges  contains 
kotlin.ranges  copyTo 
kotlin.ranges  endsWith 
kotlin.ranges  first 
kotlin.ranges  forEach 
kotlin.ranges  getValue 
kotlin.ranges  indexOf 
kotlin.ranges  
isNullOrBlank 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  java 
kotlin.ranges  	lastIndex 
kotlin.ranges  lazy 
kotlin.ranges  provideDelegate 
kotlin.ranges  reduceMimeTypes 
kotlin.ranges  require 
kotlin.ranges  result 
kotlin.ranges  
startsWith 
kotlin.ranges  	substring 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  
ACTIVITY_CODE kotlin.sequences  	ArrayList kotlin.sequences  
AtomicBoolean kotlin.sequences  Build kotlin.sequences  CHANNEL kotlin.sequences  
ComponentName kotlin.sequences  File kotlin.sequences  FileProvider kotlin.sequences  IOException kotlin.sequences  IllegalArgumentException kotlin.sequences  Intent kotlin.sequences  MethodCallHandler kotlin.sequences  
MethodChannel kotlin.sequences  PackageManager kotlin.sequences  
PendingIntent kotlin.sequences  RESULT_UNAVAILABLE kotlin.sequences  Share kotlin.sequences  SharePlusPendingIntent kotlin.sequences  ShareSuccessManager kotlin.sequences  Throws kotlin.sequences  apply kotlin.sequences  contains kotlin.sequences  copyTo kotlin.sequences  endsWith kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  getValue kotlin.sequences  indexOf kotlin.sequences  
isNullOrBlank kotlin.sequences  
isNullOrEmpty kotlin.sequences  java kotlin.sequences  	lastIndex kotlin.sequences  lazy kotlin.sequences  provideDelegate kotlin.sequences  reduceMimeTypes kotlin.sequences  require kotlin.sequences  result kotlin.sequences  
startsWith kotlin.sequences  	substring kotlin.sequences  
ACTIVITY_CODE kotlin.text  	ArrayList kotlin.text  
AtomicBoolean kotlin.text  Build kotlin.text  CHANNEL kotlin.text  
ComponentName kotlin.text  File kotlin.text  FileProvider kotlin.text  IOException kotlin.text  IllegalArgumentException kotlin.text  Intent kotlin.text  MethodCallHandler kotlin.text  
MethodChannel kotlin.text  PackageManager kotlin.text  
PendingIntent kotlin.text  RESULT_UNAVAILABLE kotlin.text  Share kotlin.text  SharePlusPendingIntent kotlin.text  ShareSuccessManager kotlin.text  Throws kotlin.text  apply kotlin.text  contains kotlin.text  copyTo kotlin.text  endsWith kotlin.text  first kotlin.text  forEach kotlin.text  getValue kotlin.text  indexOf kotlin.text  
isNullOrBlank kotlin.text  
isNullOrEmpty kotlin.text  java kotlin.text  	lastIndex kotlin.text  lazy kotlin.text  provideDelegate kotlin.text  reduceMimeTypes kotlin.text  require kotlin.text  result kotlin.text  
startsWith kotlin.text  	substring kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       