import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Environment configuration class to manage environment variables
class EnvConfig {
  /// Initialize environment configuration
  /// For mobile, we try to load from .env files with fallback to hardcoded values
  static Future<void> init({String env = 'production'}) async {
    // For mobile, try to load from .env files
    try {
      String fileName;

      // Determine which environment file to load
      if (env == 'development') {
        fileName = '.env.development';
      } else if (env == 'production') {
        fileName = '.env.production';
      } else if (env == 'staging') {
        fileName = '.env'; // Use main .env file for staging
      } else {
        fileName = '.env';
      }

      // Load the environment file
      await dotenv.load(fileName: fileName);

      // Print loaded environment variables for debugging
      debugPrint('Environment loaded: $env');
      debugPrint('API Base URL: $apiBaseUrl');
      debugPrint('API Storage URL: $apiStorageUrl');
    } catch (e) {
      debugPrint('Error loading environment: $e');
      // If we can't load the env file, we'll use default values in the constants
      debugPrint('Using default values');
    }
  }

  /// Get API base URL from environment variables
  static String get apiBaseUrl {
    return dotenv.env['API_BASE_URL'] ??
        'http://medroid-full.test/api/'; // Use local Laragon backend for development
  }

  /// Get API storage URL from environment variables
  static String get apiStorageUrl {
    return dotenv.env['API_STORAGE_URL'] ??
        'http://medroid-full.test/'; // Use local Laragon backend for development
  }

  /// Get Agora App ID from environment variables
  static String get agoraAppId {
    return dotenv.env['AGORA_APP_ID'] ??
        '********************************'; // Fallback for development
  }

  /// Get Agora App Certificate from environment variables
  static String get agoraAppCertificate {
    return dotenv.env['AGORA_APP_CERTIFICATE'] ??
        '********************************'; // Fallback for development
  }

  /// Get Agora Token Service URL from environment variables
  static String? get agoraTokenServiceUrl {
    final url = dotenv.env['AGORA_TOKEN_SERVICE_URL'];
    return (url != null && url.isNotEmpty) ? url : null;
  }

  /// Get Stripe publishable key from environment variables
  static String get stripePublishableKey {
    return dotenv.env['STRIPE_PUBLISHABLE_KEY'] ??
        'pk_test_wgyClonVuxwxR6W3xCMEWw8K'; // Matches Laravel backend configuration
  }

  /// Get Stripe webhook secret from environment variables
  static String? get stripeWebhookSecret {
    final secret = dotenv.env['STRIPE_WEBHOOK_SECRET'];
    return (secret != null && secret.isNotEmpty) ? secret : null;
  }

  /// Check if Stripe webhook verification is enabled
  static bool get stripeWebhookVerify {
    return dotenv.env['STRIPE_WEBHOOK_VERIFY']?.toLowerCase() == 'true';
  }

  /// Get app name from environment variables
  static String get appName {
    return dotenv.env['APP_NAME'] ?? 'Medroid';
  }

  /// Get app version from environment variables
  static String get appVersion {
    return dotenv.env['APP_VERSION'] ?? '1.0.0';
  }

  /// Check if debug mode is enabled
  static bool get isDebugMode {
    return dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true' || kDebugMode;
  }

  /// Check if shop feature is enabled
  static bool get isShopEnabled {
    return dotenv.env['ENABLE_SHOP']?.toLowerCase() == 'true' ?? true;
  }

  /// Check if video consultation is enabled
  static bool get isVideoConsultationEnabled {
    return dotenv.env['ENABLE_VIDEO_CONSULTATION']?.toLowerCase() == 'true' ??
        true;
  }

  /// Check if AI chat is enabled
  static bool get isAiChatEnabled {
    return dotenv.env['ENABLE_AI_CHAT']?.toLowerCase() == 'true' ?? true;
  }
}
