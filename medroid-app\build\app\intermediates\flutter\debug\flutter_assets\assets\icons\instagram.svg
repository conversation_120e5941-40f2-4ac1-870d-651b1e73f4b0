<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
  <linearGradient id="insta-gradient" x1="0%" y1="100%" x2="100%" y2="0%">
    <stop offset="0%" style="stop-color:#FFDC80" />
    <stop offset="25%" style="stop-color:#FCAF45" />
    <stop offset="50%" style="stop-color:#F77737" />
    <stop offset="75%" style="stop-color:#F56040" />
    <stop offset="100%" style="stop-color:#FD1D1D" />
  </linearGradient>
  <circle cx="12" cy="12" r="11" fill="url(#insta-gradient)" />
  <circle cx="12" cy="12" r="5" fill="none" stroke="white" stroke-width="2" />
  <circle cx="18" cy="6" r="1.5" fill="white" />
</svg>
