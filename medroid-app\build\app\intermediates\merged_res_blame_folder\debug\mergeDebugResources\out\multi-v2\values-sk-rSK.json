{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-sk-rSK/values-sk-rSK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-sk-rSK\\values-sk-rSK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,141,237,366,466,558,683,849,1040,1178,1267,1375,1468,1564,1656,1719,1784,1877,1978,2192,2276,2363,2437,2512,2647,2743,2840,2911,2971,3071,3169,3303,3400,3473,3547,3633,3703,3822,3935", "endColumns": "85,95,128,99,91,124,165,190,137,88,107,92,95,91,62,64,92,100,213,83,86,73,74,134,95,96,70,59,99,97,133,96,72,73,85,69,118,112,82", "endOffsets": "136,232,361,461,553,678,844,1035,1173,1262,1370,1463,1559,1651,1714,1779,1872,1973,2187,2271,2358,2432,2507,2642,2738,2835,2906,2966,3066,3164,3298,3395,3468,3542,3628,3698,3817,3930,4013"}, "to": {"startLines": "17,95,112,122,123,170,176,177,178,179,181,182,183,185,186,187,188,189,190,191,192,193,194,195,200,201,202,203,204,205,206,207,208,226,233,234,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,7941,9898,10911,11011,14979,15461,15627,15818,15956,16148,16256,16349,16560,16652,16715,16780,16873,16974,17188,17272,17359,17433,17508,17974,18070,18167,18238,18298,18398,18496,18630,18727,21269,21774,21860,21930,22049,22162", "endColumns": "85,95,128,99,91,124,165,190,137,88,107,92,95,91,62,64,92,100,213,83,86,73,74,134,95,96,70,59,99,97,133,96,72,73,85,69,118,112,82", "endOffsets": "1235,8032,10022,11006,11098,15099,15622,15813,15951,16040,16251,16344,16440,16647,16710,16775,16868,16969,17183,17267,17354,17428,17503,17638,18065,18162,18233,18293,18393,18491,18625,18722,18795,21338,21855,21925,22044,22157,22240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-sk-rSK\\values-sk-rSK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,269,335,430,502,747,811,902,971,1025,1089,1410,1486,1554,1608,1661,1736,1854,1962,2020,2101,2180,2267,2350,2467,2557,2634,2696,2756,2821,2882,2985,3100,3192,3266,3345,3431,3655,3888,4007,4069,4856,4919", "endColumns": "168,44,65,94,71,244,63,90,68,53,63,320,75,67,53,52,74,117,107,57,80,78,86,82,116,89,76,61,59,64,60,102,114,91,73,78,85,223,232,118,61,786,62,71", "endOffsets": "219,264,330,425,497,742,806,897,966,1020,1084,1405,1481,1549,1603,1656,1731,1849,1957,2015,2096,2175,2262,2345,2462,2552,2629,2691,2751,2816,2877,2980,3095,3187,3261,3340,3426,3650,3883,4002,4064,4851,4914,4986"}, "to": {"startLines": "84,88,89,90,91,92,93,94,108,111,113,119,124,125,132,142,145,146,147,148,149,155,158,163,165,166,167,169,171,172,175,180,184,196,197,198,199,209,215,216,217,219,220,232", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6279,7263,7308,7374,7469,7541,7786,7850,9592,9844,10027,10491,11103,11179,11640,12564,12767,12842,12960,13068,13126,13673,13927,14391,14563,14680,14770,14917,15104,15164,15400,16045,16445,17643,17735,17809,17888,18800,19422,19655,19774,19905,20692,21702", "endColumns": "168,44,65,94,71,244,63,90,68,53,63,320,75,67,53,52,74,117,107,57,80,78,86,82,116,89,76,61,59,64,60,102,114,91,73,78,85,223,232,118,61,786,62,71", "endOffsets": "6443,7303,7369,7464,7536,7781,7845,7936,9656,9893,10086,10807,11174,11242,11689,12612,12837,12955,13063,13121,13202,13747,14009,14469,14675,14765,14842,14974,15159,15224,15456,16143,16555,17730,17804,17883,17969,19019,19650,19769,19831,20687,20750,21769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-sk-rSK\\values-sk-rSK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,194,259,347,408,477,536,614,679,747,807", "endColumns": "78,59,64,87,60,68,58,77,64,67,59,61", "endOffsets": "129,189,254,342,403,472,531,609,674,742,802,864"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1682,2490,2631,2696,2784,3061,3589,3779,4033,4329,4679,4970", "endColumns": "78,59,64,87,60,68,58,77,64,67,59,61", "endOffsets": "1756,2545,2691,2779,2840,3125,3643,3852,4093,4392,4734,5027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-sk-rSK\\values-sk-rSK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,202,314,384,475,536,606,689,809,989,1064,1135,1212,1307,1419,1575,1666,1739,1897,1964,2025,2097,2178,2305,2421,2491,2594,2683,2771,2861,2934", "endColumns": "71,74,111,69,90,60,69,82,119,179,74,70,76,94,111,155,90,72,157,66,60,71,80,126,115,69,102,88,87,89,72,155", "endOffsets": "122,197,309,379,470,531,601,684,804,984,1059,1130,1207,1302,1414,1570,1661,1734,1892,1959,2020,2092,2173,2300,2416,2486,2589,2678,2766,2856,2929,3085"}, "to": {"startLines": "16,18,152,168,210,221,222,223,224,225,238,239,240,241,242,243,244,245,247,248,249,250,251,252,253,254,255,256,257,258,259,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1082,1240,13398,14847,19024,20755,20816,20886,20969,21089,22245,22320,22391,22468,22563,22675,22831,22922,23066,23224,23291,23352,23424,23505,23632,23748,23818,23921,24010,24098,24188,24261", "endColumns": "71,74,111,69,90,60,69,82,119,179,74,70,76,94,111,155,90,72,157,66,60,71,80,126,115,69,102,88,87,89,72,155", "endOffsets": "1149,1310,13505,14912,19110,20811,20881,20964,21084,21264,22315,22386,22463,22558,22670,22826,22917,22990,23219,23286,23347,23419,23500,23627,23743,23813,23916,24005,24093,24183,24256,24412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-sk-rSK\\values-sk-rSK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,347,439,492,551,617,693,778,854,954,1054,1139,1217,1298,1380,1479,1565,1627,1710,1799,1885,1992,2073,2156,2225,2315,2391,2485,2570,2651,2730,2827,2899,2970,3081,3157,3221,3896,4548,4621,4724,4824,4879,4964,5047,5117,5213,5310,5367,5451,5500,5579,5681,5757,5851,5901,5982,6075,6122,6170,6248,6306,6380,6562,6707,6831,6896,6981,7058,7144,7230,7319,7396,7484,7573,7655,7744,7797,7928,7980,8051,8120,8185,8256,8323,8393,8479", "endColumns": "68,77,61,82,91,52,58,65,75,84,75,99,99,84,77,80,81,98,85,61,82,88,85,106,80,82,68,89,75,93,84,80,78,96,71,70,110,75,63,674,651,72,102,99,54,84,82,69,95,96,56,83,48,78,101,75,93,49,80,92,46,47,77,57,73,181,144,123,64,84,76,85,85,88,76,87,88,81,88,52,130,51,70,68,64,70,66,69,85,70", "endOffsets": "119,197,259,342,434,487,546,612,688,773,849,949,1049,1134,1212,1293,1375,1474,1560,1622,1705,1794,1880,1987,2068,2151,2220,2310,2386,2480,2565,2646,2725,2822,2894,2965,3076,3152,3216,3891,4543,4616,4719,4819,4874,4959,5042,5112,5208,5305,5362,5446,5495,5574,5676,5752,5846,5896,5977,6070,6117,6165,6243,6301,6375,6557,6702,6826,6891,6976,7053,7139,7225,7314,7391,7479,7568,7650,7739,7792,7923,7975,8046,8115,8180,8251,8318,8388,8474,8545"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,85,86,87,96,97,98,99,100,101,102,103,104,105,106,107,114,115,116,117,118,121,126,127,128,129,134,135,136,137,138,139,143,144,153,154,156,157,161,162,164,173,174,211,212,213,214,218,227,228,229,230,231,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "586,655,733,795,878,970,1023,1315,1381,1457,1542,1761,1940,2040,2192,2550,2845,3857,4098,4184,4246,4397,4486,4572,4739,5032,5115,5184,5274,5350,5444,5529,5610,5689,5786,5937,6168,6448,6524,6588,8037,8689,8762,8865,8965,9020,9105,9188,9258,9354,9451,9508,10091,10140,10219,10321,10397,10861,11247,11328,11421,11468,11758,11836,11894,11968,12150,12295,12617,12682,13510,13587,13752,13838,14226,14303,14474,15229,15311,19115,19168,19299,19351,19836,21343,21408,21479,21546,21616,22995", "endColumns": "68,77,61,82,91,52,58,65,75,84,75,99,99,84,77,80,81,98,85,61,82,88,85,106,80,82,68,89,75,93,84,80,78,96,71,70,110,75,63,674,651,72,102,99,54,84,82,69,95,96,56,83,48,78,101,75,93,49,80,92,46,47,77,57,73,181,144,123,64,84,76,85,85,88,76,87,88,81,88,52,130,51,70,68,64,70,66,69,85,70", "endOffsets": "650,728,790,873,965,1018,1077,1376,1452,1537,1613,1856,2035,2120,2265,2626,2922,3951,4179,4241,4324,4481,4567,4674,4815,5110,5179,5269,5345,5439,5524,5605,5684,5781,5853,6003,6274,6519,6583,7258,8684,8757,8860,8960,9015,9100,9183,9253,9349,9446,9503,9587,10135,10214,10316,10392,10486,10906,11323,11416,11463,11511,11831,11889,11963,12145,12290,12414,12677,12762,13582,13668,13833,13922,14298,14386,14558,15306,15395,19163,19294,19346,19417,19900,21403,21474,21541,21611,21697,23061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-sk-rSK\\values-sk-rSK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,198,265,355,424,485,555,619,682,750,817,877,940,1014,1078,1146,1209,1286,1353,1436,1515,1592,1675,1774,1858,1907,1955,2031,2095,2170,2240,2348,2431,2539", "endColumns": "63,78,66,89,68,60,69,63,62,67,66,59,62,73,63,67,62,76,66,82,78,76,82,98,83,48,47,75,63,74,69,107,82,107,103", "endOffsets": "114,193,260,350,419,480,550,614,677,745,812,872,935,1009,1073,1141,1204,1281,1348,1431,1510,1587,1670,1769,1853,1902,1950,2026,2090,2165,2235,2343,2426,2534,2638"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,109,110,120,130,131,133,140,141,150,151,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1618,1861,2125,2270,2360,2429,2927,2997,3130,3193,3261,3328,3388,3451,3525,3648,3716,3956,4820,4887,5858,6008,6085,9661,9760,10812,11516,11564,11694,12419,12494,13207,13315,14014,14122", "endColumns": "63,78,66,89,68,60,69,63,62,67,66,59,62,73,63,67,62,76,66,82,78,76,82,98,83,48,47,75,63,74,69,107,82,107,103", "endOffsets": "1677,1935,2187,2355,2424,2485,2992,3056,3188,3256,3323,3383,3446,3520,3584,3711,3774,4028,4882,4965,5932,6080,6163,9755,9839,10856,11559,11635,11753,12489,12559,13310,13393,14117,14221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-sk-rSK\\values-sk-rSK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,243,310,378,442,517", "endColumns": "85,101,66,67,63,74,68", "endOffsets": "136,238,305,373,437,512,581"}}]}]}