["C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/apple_logo.svg", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/appointment.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/doctor.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/google_logo.svg", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/headache.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/Medroid%20Full%20Logo.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/medroid_icon.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/medroid_icon_dark.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/mental.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/README.md", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/skin.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/sleep.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/subtle_pattern.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/subtle_pattern_new.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/weight.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/instagram.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/instagram.svg", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/medroid.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/medroid.svg", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/README.md", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/social.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/social.svg", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/tiktok.png", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/tiktok.svg", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\.env", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/NotoSans-Regular.ttf", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/NotoSansCJKjp-Regular.otf", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/NotoSansSymbols-Regular.ttf", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/record_web/assets/js/record.worklet.js", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/record_web/assets/js/record.fixwebmduration.js", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/wakelock_plus/assets/no_sleep.js", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\laragon\\www\\medroid-app\\medroid-full\\medroid-app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]