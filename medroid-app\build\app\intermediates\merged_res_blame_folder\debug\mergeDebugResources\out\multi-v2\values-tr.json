{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3424,3521,3623,3721,3818,3920,4026,13846", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3516,3618,3716,3813,3915,4021,4132,13942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "70,88,89,90", "startColumns": "4,4,4,4", "startOffsets": "7006,8465,8571,8678", "endColumns": "99,105,106,105", "endOffsets": "7101,8566,8673,8779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1036,1127,1204,1265,1356,1419,1482,1541,1610,1673,1727,1835,1893,1955,2009,2082,2203,2287,2378,2518,2595,2671,2758,2811,2865,2931,3001,3078,3161,3232,3307,3385,3456,3541,3630,3725,3818,3890,3962,4058,4110,4177,4261,4351,4413,4477,4540,4634,4730,4819,4916,4973,5031", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,86,52,53,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,56,57,78", "endOffsets": "260,335,410,487,586,677,773,885,967,1031,1122,1199,1260,1351,1414,1477,1536,1605,1668,1722,1830,1888,1950,2004,2077,2198,2282,2373,2513,2590,2666,2753,2806,2860,2926,2996,3073,3156,3227,3302,3380,3451,3536,3625,3720,3813,3885,3957,4053,4105,4172,4256,4346,4408,4472,4535,4629,4725,4814,4911,4968,5026,5105"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3082,3157,3234,3333,4137,4233,4345,7466,8374,8845,9105,9166,9257,9320,9383,9442,9511,9574,9628,9736,9794,9856,9910,9983,10104,10188,10279,10419,10496,10572,10659,10712,10766,10832,10902,10979,11062,11133,11208,11286,11357,11442,11531,11626,11719,11791,11863,11959,12011,12078,12162,12252,12314,12378,12441,12535,12631,12720,12817,12874,13316", "endLines": "5,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,86,52,53,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,56,57,78", "endOffsets": "310,3077,3152,3229,3328,3419,4228,4340,4422,7525,8460,8917,9161,9252,9315,9378,9437,9506,9569,9623,9731,9789,9851,9905,9978,10099,10183,10274,10414,10491,10567,10654,10707,10761,10827,10897,10974,11057,11128,11203,11281,11352,11437,11526,11621,11714,11786,11858,11954,12006,12073,12157,12247,12309,12373,12436,12530,12626,12715,12812,12869,12927,13390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5700", "endColumns": "146", "endOffsets": "5842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,198,308,371,463,522,579,658,763,934,1021,1094,1174,1265,1395,1546,1623,1697,1876,1942,2003,2077,2166,2307,2432,2499,2614,2698,2780,2866,2935", "endColumns": "69,72,109,62,91,58,56,78,104,170,86,72,79,90,129,150,76,73,178,65,60,73,88,140,124,66,114,83,81,85,68,160", "endOffsets": "120,193,303,366,458,517,574,653,758,929,1016,1089,1169,1260,1390,1541,1618,1692,1871,1937,1998,2072,2161,2302,2427,2494,2609,2693,2775,2861,2930,3091"}, "to": {"startLines": "171,173,307,323,365,376,377,378,379,380,393,394,395,396,397,398,399,400,402,403,404,405,406,407,408,409,410,411,412,413,414,415", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14972,15124,27242,28594,32743,34409,34468,34525,34604,34709,35851,35938,36011,36091,36182,36312,36463,36540,36690,36869,36935,36996,37070,37159,37300,37425,37492,37607,37691,37773,37859,37928", "endColumns": "69,72,109,62,91,58,56,78,104,170,86,72,79,90,129,150,76,73,178,65,60,73,88,140,124,66,114,83,81,85,68,160", "endOffsets": "15037,15192,27347,28652,32830,34463,34520,34599,34704,34875,35933,36006,36086,36177,36307,36458,36535,36609,36864,36930,36991,37065,37154,37295,37420,37487,37602,37686,37768,37854,37923,38084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,230,361,456,551,667,848,1048,1199,1286,1387,1480,1576,1675,1735,1798,1896,2001,2209,2291,2376,2445,2518,2635,2722,2818,2898,2958,3064,3175,3305,3399,3471,3544,3635,3707,3819,3937", "endColumns": "81,92,130,94,94,115,180,199,150,86,100,92,95,98,59,62,97,104,207,81,84,68,72,116,86,95,79,59,105,110,129,93,71,72,90,71,111,117,88", "endOffsets": "132,225,356,451,546,662,843,1043,1194,1281,1382,1475,1571,1670,1730,1793,1891,1996,2204,2286,2371,2440,2513,2630,2717,2813,2893,2953,3059,3170,3300,3394,3466,3539,3630,3702,3814,3932,4021"}, "to": {"startLines": "172,250,267,277,278,325,331,332,333,334,336,337,338,340,341,342,343,344,345,346,347,348,349,350,355,356,357,358,359,360,361,362,363,381,388,389,390,391,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15042,21834,23756,24776,24871,28719,29187,29368,29568,29719,29905,30006,30099,30295,30394,30454,30517,30615,30720,30928,31010,31095,31164,31237,31696,31783,31879,31959,32019,32125,32236,32366,32460,34880,35369,35460,35532,35644,35762", "endColumns": "81,92,130,94,94,115,180,199,150,86,100,92,95,98,59,62,97,104,207,81,84,68,72,116,86,95,79,59,105,110,129,93,71,72,90,71,111,117,88", "endOffsets": "15119,21922,23882,24866,24961,28830,29363,29563,29714,29801,30001,30094,30190,30389,30449,30512,30610,30715,30923,31005,31090,31159,31232,31349,31778,31874,31954,32014,32120,32231,32361,32455,32527,34948,35455,35527,35639,35757,35846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba2bbf823ab5a18be680588e75f7e6ba\\transformed\\jetified-play-services-wallet-19.2.1\\res\\values-tr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "91,422", "startColumns": "4,4", "startOffsets": "8784,38683", "endColumns": "60,78", "endOffsets": "8840,38757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e71e7c08b297cb32539b89f832f53d8\\transformed\\jetified-material3-1.0.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,216", "endColumns": "79,80,80", "endOffsets": "130,211,292"}, "to": {"startLines": "50,73,86", "startColumns": "4,4,4", "startOffsets": "4604,7301,8293", "endColumns": "79,80,80", "endOffsets": "4679,7377,8369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,13695", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,13770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,193,255,338,399,474,531,610,682,745,807", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "128,188,250,333,394,469,526,605,677,740,802,871"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15549,16351,16495,16557,16640,16921,17461,17651,17918,18248,18583,18875", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "15622,16406,16552,16635,16696,16991,17513,17725,17985,18306,18640,18939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "69,85,147,151,416,420,421", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6935,8206,13104,13395,38089,38522,38605", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "7001,8288,13177,13522,38253,38600,38678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,345,431,491,557,609,689,765,846,933,1035,1122,1204,1288,1373,1476,1572,1641,1734,1821,1900,2006,2092,2185,2261,2361,2439,2536,2623,2706,2789,2887,2963,3046,3155,3224,3291,3953,4591,4668,4774,4873,4928,5012,5096,5164,5253,5345,5400,5480,5528,5604,5710,5779,5849,5896,5979,6076,6122,6170,6252,6310,6377,6551,6705,6834,6902,6982,7056,7143,7223,7305,7376,7464,7532,7613,7703,7758,7881,7930,7987,8052,8114,8184,8251,8320,8407", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,46,82,96,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,80,89,54,122,48,56,64,61,69,66,68,86,75", "endOffsets": "121,201,263,340,426,486,552,604,684,760,841,928,1030,1117,1199,1283,1368,1471,1567,1636,1729,1816,1895,2001,2087,2180,2256,2356,2434,2531,2618,2701,2784,2882,2958,3041,3150,3219,3286,3948,4586,4663,4769,4868,4923,5007,5091,5159,5248,5340,5395,5475,5523,5599,5705,5774,5844,5891,5974,6071,6117,6165,6247,6305,6372,6546,6700,6829,6897,6977,7051,7138,7218,7300,7371,7459,7527,7608,7698,7753,7876,7925,7982,8047,8109,8179,8246,8315,8402,8478"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,240,241,242,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,272,273,276,281,282,283,284,289,290,291,292,293,294,298,299,308,309,311,312,316,317,319,328,329,366,367,368,369,373,382,383,384,385,386,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14470,14541,14621,14683,14760,14846,14906,15197,15249,15329,15405,15627,15792,15894,16049,16411,16701,17730,17990,18086,18155,18311,18398,18477,18645,18944,19037,19113,19213,19291,19388,19475,19558,19641,19739,19889,20136,20404,20473,20540,21927,22565,22642,22748,22847,22902,22986,23070,23138,23227,23319,23374,23951,23999,24075,24181,24250,24729,25106,25189,25286,25332,25619,25701,25759,25826,26000,26154,26476,26544,27352,27426,27589,27669,28024,28095,28255,28955,29036,32835,32890,33013,33062,33520,34953,35015,35085,35152,35221,36614", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,46,82,96,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,80,89,54,122,48,56,64,61,69,66,68,86,75", "endOffsets": "14536,14616,14678,14755,14841,14901,14967,15244,15324,15400,15481,15709,15889,15976,16126,16490,16781,17828,18081,18150,18243,18393,18472,18578,18726,19032,19108,19208,19286,19383,19470,19553,19636,19734,19810,19967,20240,20468,20535,21197,22560,22637,22743,22842,22897,22981,23065,23133,23222,23314,23369,23449,23994,24070,24176,24245,24315,24771,25184,25281,25327,25375,25696,25754,25821,25995,26149,26278,26539,26619,27421,27508,27664,27746,28090,28178,28318,29031,29121,32885,33008,33057,33114,33580,35010,35080,35147,35216,35303,36685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4794,4949,5085,5190,5337,5467,5594,5847,6019,6126,6283,6417,6562,6729,6791,6855", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "4789,4944,5080,5185,5332,5462,5589,5695,6014,6121,6278,6412,6557,6724,6786,6850,6930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be80f888898887bb71616e48bf7874ba\\transformed\\jetified-ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "48,49,71,72,74,93,94,145,146,148,149,152,153,155,417,418,419", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4427,4520,7106,7201,7382,8922,9005,12932,13020,13182,13250,13527,13607,13775,38258,38336,38404", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "4515,4599,7196,7296,7461,9000,9100,13015,13099,13245,13311,13602,13690,13841,38331,38399,38517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,196,264,350,423,484,556,619,682,750,818,885,945,1020,1084,1154,1217,1302,1366,1446,1520,1599,1684,1787,1866,1917,1966,2039,2103,2167,2240,2336,2418,2512", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "113,191,259,345,418,479,551,614,677,745,813,880,940,1015,1079,1149,1212,1297,1361,1441,1515,1594,1679,1782,1861,1912,1961,2034,2098,2162,2235,2331,2413,2507,2601"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,264,265,275,285,286,288,295,296,305,306,314,315", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15486,15714,15981,16131,16217,16290,16786,16858,16996,17059,17127,17195,17262,17322,17397,17518,17588,17833,18731,18795,19815,19972,20051,23519,23622,24678,25380,25429,25555,26283,26347,27064,27160,27836,27930", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "15544,15787,16044,16212,16285,16346,16853,16916,17054,17122,17190,17257,17317,17392,17456,17583,17646,17913,18790,18870,19884,20046,20131,23617,23696,24724,25424,25497,25614,26342,26415,27155,27237,27925,28019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,259,329,418,490,705,765,846,911,966,1030,1388,1462,1528,1581,1637,1711,1833,1944,2001,2077,2153,2238,2310,2422,2504,2581,2643,2703,2763,2824,2923,3023,3123,3196,3275,3365,3576,3800,3921,3977,4740,4801", "endColumns": "158,44,69,88,71,214,59,80,64,54,63,357,73,65,52,55,73,121,110,56,75,75,84,71,111,81,76,61,59,59,60,98,99,99,72,78,89,210,223,120,55,762,60,60", "endOffsets": "209,254,324,413,485,700,760,841,906,961,1025,1383,1457,1523,1576,1632,1706,1828,1939,1996,2072,2148,2233,2305,2417,2499,2576,2638,2698,2758,2819,2918,3018,3118,3191,3270,3360,3571,3795,3916,3972,4735,4796,4857"}, "to": {"startLines": "239,243,244,245,246,247,248,249,263,266,268,274,279,280,287,297,300,301,302,303,304,310,313,318,320,321,322,324,326,327,330,335,339,351,352,353,354,364,370,371,372,374,375,387", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20245,21202,21247,21317,21406,21478,21693,21753,23454,23701,23887,24320,24966,25040,25502,26420,26624,26698,26820,26931,26988,27513,27751,28183,28323,28435,28517,28657,28835,28895,29126,29806,30195,31354,31454,31527,31606,32532,33119,33343,33464,33585,34348,35308", "endColumns": "158,44,69,88,71,214,59,80,64,54,63,357,73,65,52,55,73,121,110,56,75,75,84,71,111,81,76,61,59,59,60,98,99,99,72,78,89,210,223,120,55,762,60,60", "endOffsets": "20399,21242,21312,21401,21473,21688,21748,21829,23514,23751,23946,24673,25035,25101,25550,26471,26693,26815,26926,26983,27059,27584,27831,28250,28430,28512,28589,28714,28890,28950,29182,29900,30290,31449,31522,31601,31691,32738,33338,33459,33515,34343,34404,35364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7530,7607,7666,7731,7792,7872,7944,8034,8130", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "7602,7661,7726,7787,7867,7939,8029,8125,8201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,140,239,308,377,440,511", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "135,234,303,372,435,506,573"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13947,14032,14131,14200,14269,14332,14403", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "14027,14126,14195,14264,14327,14398,14465"}}]}]}