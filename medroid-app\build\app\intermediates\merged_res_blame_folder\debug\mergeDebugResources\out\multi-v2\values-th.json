{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,199,315,374,459,513,570,653,753,914,988,1058,1128,1219,1336,1461,1532,1603,1780,1852,1914,1987,2062,2184,2294,2358,2458,2544,2621,2704,2769", "endColumns": "68,74,115,58,84,53,56,82,99,160,73,69,69,90,116,124,70,70,176,71,61,72,74,121,109,63,99,85,76,82,64,137", "endOffsets": "119,194,310,369,454,508,565,648,748,909,983,1053,1123,1214,1331,1456,1527,1598,1775,1847,1909,1982,2057,2179,2289,2353,2453,2539,2616,2699,2764,2902"}, "to": {"startLines": "164,166,300,316,358,369,370,371,372,373,386,387,388,389,390,391,392,393,395,396,397,398,399,400,401,402,403,404,405,406,407,408", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14193,14345,26263,27574,31635,33099,33153,33210,33293,33393,34495,34569,34639,34709,34800,34917,35042,35113,35263,35440,35512,35574,35647,35722,35844,35954,36018,36118,36204,36281,36364,36429", "endColumns": "68,74,115,58,84,53,56,82,99,160,73,69,69,90,116,124,70,70,176,71,61,72,74,121,109,63,99,85,76,82,64,137", "endOffsets": "14257,14415,26374,27628,31715,33148,33205,33288,33388,33549,34564,34634,34704,34795,34912,35037,35108,35179,35435,35507,35569,35642,35717,35839,35949,36013,36113,36199,36276,36359,36424,36562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,332,409,471,539,594,669,745,824,934,1044,1138,1231,1319,1411,1517,1619,1690,1789,1883,1971,2087,2174,2273,2351,2457,2527,2616,2702,2785,2866,2965,3033,3107,3208,3276,3342,3916,4469,4544,4650,4744,4799,4886,4970,5036,5120,5203,5260,5334,5383,5466,5564,5633,5704,5749,5824,5918,5968,6014,6084,6142,6207,6374,6527,6643,6708,6786,6856,6943,7020,7100,7169,7263,7333,7413,7499,7549,7659,7707,7764,7837,7899,7967,8033,8105,8188", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,44,74,93,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,79,85,49,109,47,56,72,61,67,65,71,82,78", "endOffsets": "119,197,259,327,404,466,534,589,664,740,819,929,1039,1133,1226,1314,1406,1512,1614,1685,1784,1878,1966,2082,2169,2268,2346,2452,2522,2611,2697,2780,2861,2960,3028,3102,3203,3271,3337,3911,4464,4539,4645,4739,4794,4881,4965,5031,5115,5198,5255,5329,5378,5461,5559,5628,5699,5744,5819,5913,5963,6009,6079,6137,6202,6369,6522,6638,6703,6781,6851,6938,7015,7095,7164,7258,7328,7408,7494,7544,7654,7702,7759,7832,7894,7962,8028,8100,8183,8262"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,233,234,235,244,245,246,247,248,249,250,251,252,253,254,255,262,263,264,265,266,269,274,275,276,277,282,283,284,285,286,287,291,292,301,302,304,305,309,310,312,321,322,359,360,361,362,366,375,376,377,378,379,394", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13709,13778,13856,13918,13986,14063,14125,14420,14475,14550,14626,14852,15044,15154,15316,15682,15983,17015,17286,17388,17459,17624,17718,17806,17981,18303,18402,18480,18586,18656,18745,18831,18914,18995,19094,19238,19475,19736,19804,19870,21169,21722,21797,21903,21997,22052,22139,22223,22289,22373,22456,22513,23091,23140,23223,23321,23390,23819,24198,24273,24367,24417,24695,24765,24823,24888,25055,25208,25513,25578,26379,26449,26610,26687,27025,27094,27256,27944,28024,31720,31770,31880,31928,32368,33631,33693,33761,33827,33899,35184", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,44,74,93,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,79,85,49,109,47,56,72,61,67,65,71,82,78", "endOffsets": "13773,13851,13913,13981,14058,14120,14188,14470,14545,14621,14700,14957,15149,15243,15404,15765,16070,17116,17383,17454,17553,17713,17801,17917,18063,18397,18475,18581,18651,18740,18826,18909,18990,19089,19157,19307,19571,19799,19865,20439,21717,21792,21898,21992,22047,22134,22218,22284,22368,22451,22508,22582,23135,23218,23316,23385,23456,23859,24268,24362,24412,24458,24760,24818,24883,25050,25203,25319,25573,25651,26444,26531,26682,26762,27089,27183,27321,28019,28105,31765,31875,31923,31980,32436,33688,33756,33822,33894,33977,35258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1046,1136,1206,1266,1353,1419,1484,1545,1609,1670,1724,1825,1886,1946,2000,2070,2181,2268,2349,2492,2571,2653,2745,2799,2852,2918,2988,3066,3152,3224,3302,3371,3440,3522,3610,3703,3797,3871,3940,4035,4087,4155,4240,4328,4390,4454,4517,4617,4710,4807,4900,4958,5015", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,91,53,52,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,57,56,76", "endOffsets": "280,353,425,508,593,679,778,891,971,1041,1131,1201,1261,1348,1414,1479,1540,1604,1665,1719,1820,1881,1941,1995,2065,2176,2263,2344,2487,2566,2648,2740,2794,2847,2913,2983,3061,3147,3219,3297,3366,3435,3517,3605,3698,3792,3866,3935,4030,4082,4150,4235,4323,4385,4449,4512,4612,4705,4802,4895,4953,5010,5087"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,7265,8174,8633,8873,8933,9020,9086,9151,9212,9276,9337,9391,9492,9553,9613,9667,9737,9848,9935,10016,10159,10238,10320,10412,10466,10519,10585,10655,10733,10819,10891,10969,11038,11107,11189,11277,11370,11464,11538,11607,11702,11754,11822,11907,11995,12057,12121,12184,12284,12377,12474,12567,12625,13062", "endLines": "5,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,91,53,52,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,57,56,76", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,7330,8259,8698,8928,9015,9081,9146,9207,9271,9332,9386,9487,9548,9608,9662,9732,9843,9930,10011,10154,10233,10315,10407,10461,10514,10580,10650,10728,10814,10886,10964,11033,11102,11184,11272,11365,11459,11533,11602,11697,11749,11817,11902,11990,12052,12116,12179,12279,12372,12469,12562,12620,12677,13134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be80f888898887bb71616e48bf7874ba\\transformed\\jetified-ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "48,49,71,72,74,93,94,145,146,148,149,152,153,155,410,411,412", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4370,4456,6902,6999,7177,8703,8788,12682,12768,12931,12996,13278,13364,13535,36735,36813,36880", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "4451,4528,6994,7095,7260,8783,8868,12763,12846,12991,13057,13359,13448,13603,36808,36875,36998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "69,85,147,151,409,413,414", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6729,8016,12851,13139,36567,37003,37083", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "6794,8097,12926,13273,36730,37078,37156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,13453", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,13530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e71e7c08b297cb32539b89f832f53d8\\transformed\\jetified-material3-1.0.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,204", "endColumns": "71,76,71", "endOffsets": "122,199,271"}, "to": {"startLines": "50,73,86", "startColumns": "4,4,4", "startOffsets": "4533,7100,8102", "endColumns": "71,76,71", "endOffsets": "4600,7172,8169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,261,346,410,482,541,619,693,759,818", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "132,192,256,341,405,477,536,614,688,754,813,884"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14770,15622,15770,15834,15919,16202,16747,16937,17212,17558,17922,18232", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "14847,15677,15829,15914,15978,16269,16801,17010,17281,17619,17976,18298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7335,7411,7474,7542,7610,7687,7760,7851,7937", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "7406,7469,7537,7605,7682,7755,7846,7932,8011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,264,332,421,492,704,764,852,929,984,1048,1356,1424,1494,1547,1600,1677,1792,1888,1956,2033,2107,2191,2259,2355,2429,2507,2566,2628,2689,2751,2846,2929,3048,3121,3200,3303,3506,3718,3835,3889,4485,4547", "endColumns": "159,48,67,88,70,211,59,87,76,54,63,307,67,69,52,52,76,114,95,67,76,73,83,67,95,73,77,58,61,60,61,94,82,118,72,78,102,202,211,116,53,595,61,54", "endOffsets": "210,259,327,416,487,699,759,847,924,979,1043,1351,1419,1489,1542,1595,1672,1787,1883,1951,2028,2102,2186,2254,2350,2424,2502,2561,2623,2684,2746,2841,2924,3043,3116,3195,3298,3501,3713,3830,3884,4480,4542,4597"}, "to": {"startLines": "232,236,237,238,239,240,241,242,256,259,261,267,272,273,280,290,293,294,295,296,297,303,306,311,313,314,315,317,319,320,323,328,332,344,345,346,347,357,363,364,365,367,368,380", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19576,20444,20493,20561,20650,20721,20933,20993,22587,22850,23027,23461,24060,24128,24578,25460,25656,25733,25848,25944,26012,26536,26767,27188,27326,27422,27496,27633,27821,27883,28110,28787,29172,30262,30381,30454,30533,31432,31985,32197,32314,32441,33037,33982", "endColumns": "159,48,67,88,70,211,59,87,76,54,63,307,67,69,52,52,76,114,95,67,76,73,83,67,95,73,77,58,61,60,61,94,82,118,72,78,102,202,211,116,53,595,61,54", "endOffsets": "19731,20488,20556,20645,20716,20928,20988,21076,22659,22900,23086,23764,24123,24193,24626,25508,25728,25843,25939,26007,26084,26605,26846,27251,27417,27491,27569,27687,27878,27939,28167,28877,29250,30376,30449,30528,30631,31630,32192,32309,32363,33032,33094,34032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4605,4711,4858,4981,5088,5224,5348,5467,5704,5848,5953,6100,6222,6362,6513,6577,6645", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4706,4853,4976,5083,5219,5343,5462,5570,5843,5948,6095,6217,6357,6508,6572,6640,6724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,270,355,422,483,548,610,675,748,820,889,950,1019,1083,1150,1214,1305,1370,1469,1545,1623,1708,1824,1894,1944,1991,2059,2123,2182,2259,2348,2433,2522", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "115,197,265,350,417,478,543,605,670,743,815,884,945,1014,1078,1145,1209,1300,1365,1464,1540,1618,1703,1819,1889,1939,1986,2054,2118,2177,2254,2343,2428,2517,2602"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,257,258,268,278,279,281,288,289,298,299,307,308", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14705,14962,15248,15409,15494,15561,16075,16140,16274,16339,16412,16484,16553,16614,16683,16806,16873,17121,18068,18133,19162,19312,19390,22664,22780,23769,24463,24510,24631,25324,25383,26089,26178,26851,26940", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "14765,15039,15311,15489,15556,15617,16135,16197,16334,16407,16479,16548,16609,16678,16742,16868,16932,17207,18128,18227,19233,19385,19470,22775,22845,23814,24505,24573,24690,25378,25455,26173,26258,26935,27020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,226,348,451,544,673,858,1051,1200,1288,1384,1477,1578,1669,1727,1790,1882,1986,2168,2246,2332,2406,2475,2585,2676,2767,2835,2895,2995,3098,3221,3312,3381,3458,3542,3615,3720,3830", "endColumns": "82,87,121,102,92,128,184,192,148,87,95,92,100,90,57,62,91,103,181,77,85,73,68,109,90,90,67,59,99,102,122,90,68,76,83,72,104,109,85", "endOffsets": "133,221,343,446,539,668,853,1046,1195,1283,1379,1472,1573,1664,1722,1785,1877,1981,2163,2241,2327,2401,2470,2580,2671,2762,2830,2890,2990,3093,3216,3307,3376,3453,3537,3610,3715,3825,3911"}, "to": {"startLines": "165,243,260,270,271,318,324,325,326,327,329,330,331,333,334,335,336,337,338,339,340,341,342,343,348,349,350,351,352,353,354,355,356,374,381,382,383,384,385", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14262,21081,22905,23864,23967,27692,28172,28357,28550,28699,28882,28978,29071,29255,29346,29404,29467,29559,29663,29845,29923,30009,30083,30152,30636,30727,30818,30886,30946,31046,31149,31272,31363,33554,34037,34121,34194,34299,34409", "endColumns": "82,87,121,102,92,128,184,192,148,87,95,92,100,90,57,62,91,103,181,77,85,73,68,109,90,90,67,59,99,102,122,90,68,76,83,72,104,109,85", "endOffsets": "14340,21164,23022,23962,24055,27816,28352,28545,28694,28782,28973,29066,29167,29341,29399,29462,29554,29658,29840,29918,30004,30078,30147,30257,30722,30813,30881,30941,31041,31144,31267,31358,31427,33626,34116,34189,34294,34404,34490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5575", "endColumns": "128", "endOffsets": "5699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba2bbf823ab5a18be680588e75f7e6ba\\transformed\\jetified-play-services-wallet-19.2.1\\res\\values-th\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "91,415", "startColumns": "4,4", "startOffsets": "8572,37161", "endColumns": "60,74", "endOffsets": "8628,37231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,13608", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,13704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "70,88,89,90", "startColumns": "4,4,4,4", "startOffsets": "6799,8264,8363,8474", "endColumns": "102,98,110,97", "endOffsets": "6897,8358,8469,8567"}}]}]}