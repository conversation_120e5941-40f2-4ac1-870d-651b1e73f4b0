import{r as b,o as $,d as o,e as a,f as w,u as h,m as R,g as y,i as t,z as T,j as i,l as c,v as p,t as d,F as _,q as U,p as B,A as G,P as q,x as F,y as z}from"./vendor-CKE3WRFf.js";import{_ as J}from"./AppLayout.vue_vue_type_script_setup_true_lang-Dvt6ywSf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B8nQAqQg.js";import"./createLucideIcon-Dy3o-9bT.js";const W={class:"flex items-center justify-between"},H={class:"flex mt-2","aria-label":"Breadcrumb"},K={class:"inline-flex items-center space-x-1 md:space-x-3"},O={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},Q={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},Y={class:"py-12"},X={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},Z={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ee={class:"p-6 text-gray-900 dark:text-gray-100"},te={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},re={key:0,class:"mt-1 text-sm text-red-600"},le={key:0,class:"mt-1 text-sm text-red-600"},se=["value"],ae={key:0,class:"mt-1 text-sm text-red-600"},oe={key:0,class:"mt-1 text-sm text-red-600"},de={key:0,class:"mt-1 text-sm text-red-600"},ue={key:0,class:"mt-1 text-sm text-red-600"},ie={key:0,class:"mt-1 text-sm text-red-600"},ne={key:0,class:"mt-1 text-sm text-red-600"},me={class:"space-y-6"},ce={class:"flex items-start space-x-4"},ge={class:"flex-1"},pe={key:0,class:"mt-1 text-sm text-red-600"},be={key:0,class:"relative"},ye=["src"],fe={class:"space-y-4"},xe={key:0,class:"text-sm text-red-600"},ve={key:1,class:"grid grid-cols-2 md:grid-cols-4 gap-4"},ke=["src","alt"],we=["onClick"],he={class:"mt-1 text-xs text-gray-500 truncate"},_e={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Ce={key:0,class:"mt-1 text-sm text-red-600"},Pe={key:0,class:"mt-1 text-sm text-red-600"},Ve={key:0,class:"mt-1 text-sm text-red-600"},Ue={class:"flex items-center space-x-6"},qe={class:"flex items-center"},Fe={class:"flex items-center"},Ie={class:"flex justify-end space-x-3"},De=["disabled"],Ae={__name:"Create",setup(Be){const C=[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"},{title:"Create Product",href:"/admin/products/create"}],f=b(!1),I=b([]),l=b({name:"",description:"",short_description:"",type:"physical",category_id:"",price:"",sale_price:"",sku:"",stock_quantity:"",manage_stock:!0,weight:"",dimensions:"",is_featured:!1,is_active:!0,digital_files:[],download_limit:"",download_expiry_days:""}),x=b(null),v=b(null),P=b([]),k=b([]),s=b({}),S=async()=>{try{const u=await window.axios.get("/admin/products/create");I.value=u.data.categories||[]}catch(u){console.error("Error fetching categories:",u)}},j=u=>{const e=u.target.files[0];if(e){x.value=e;const r=new FileReader;r.onload=n=>{v.value=n.target.result},r.readAsDataURL(e)}},E=()=>{x.value=null,v.value=null;const u=document.querySelector('input[type="file"][name="featured_image"]');u&&(u.value="")},M=u=>{Array.from(u.target.files).forEach(r=>{P.value.push(r);const n=new FileReader;n.onload=m=>{k.value.push({file:r,preview:m.target.result,name:r.name})},n.readAsDataURL(r)})},A=u=>{P.value.splice(u,1),k.value.splice(u,1)},N=async()=>{var u,e,r,n;f.value=!0,s.value={};try{const m=new FormData;Object.keys(l.value).forEach(g=>{l.value[g]!==null&&l.value[g]!==""&&(Array.isArray(l.value[g])?l.value[g].forEach((V,L)=>{m.append(`${g}[${L}]`,V)}):m.append(g,l.value[g]))}),x.value&&m.append("featured_image",x.value),P.value.forEach((g,V)=>{m.append(`gallery_images[${V}]`,g)});const D=await window.axios.post("/admin/save-product",m,{headers:{"Content-Type":"multipart/form-data"}});D.data.success?window.location.href="/admin/products":alert("Error creating product: "+D.data.message)}catch(m){(e=(u=m.response)==null?void 0:u.data)!=null&&e.errors?s.value=m.response.data.errors:alert("Error creating product: "+(((n=(r=m.response)==null?void 0:r.data)==null?void 0:n.message)||m.message))}finally{f.value=!1}};return $(()=>{S()}),(u,e)=>(a(),o(_,null,[w(h(R),{title:"Create Product"}),w(J,null,{header:y(()=>[t("div",W,[t("div",null,[e[14]||(e[14]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Create Product ",-1)),t("nav",H,[t("ol",K,[(a(),o(_,null,U(C,(r,n)=>t("li",{key:n,class:"inline-flex items-center"},[n<C.length-1?(a(),z(h(q),{key:0,href:r.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:y(()=>[F(d(r.title),1)]),_:2},1032,["href"])):(a(),o("span",O,d(r.title),1)),n<C.length-1?(a(),o("svg",Q,e[13]||(e[13]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):i("",!0)])),64))])])]),w(h(q),{href:"/admin/products",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:y(()=>e[15]||(e[15]=[F(" Back to Products ")])),_:1})])]),default:y(()=>[t("div",Y,[t("div",X,[t("div",Z,[t("div",ee,[t("form",{onSubmit:T(N,["prevent"]),class:"space-y-6"},[t("div",te,[t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Product Name *",-1)),c(t("input",{"onUpdate:modelValue":e[0]||(e[0]=r=>l.value.name=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.name]]),s.value.name?(a(),o("p",re,d(s.value.name[0]),1)):i("",!0)]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"SKU *",-1)),c(t("input",{"onUpdate:modelValue":e[1]||(e[1]=r=>l.value.sku=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.sku]]),s.value.sku?(a(),o("p",le,d(s.value.sku[0]),1)):i("",!0)]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Category *",-1)),c(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>l.value.category_id=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[18]||(e[18]=t("option",{value:""},"Select Category",-1)),(a(!0),o(_,null,U(I.value,r=>(a(),o("option",{key:r.id,value:r.id},d(r.name),9,se))),128))],512),[[B,l.value.category_id]]),s.value.category_id?(a(),o("p",ae,d(s.value.category_id[0]),1)):i("",!0)]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Type *",-1)),c(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>l.value.type=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[20]||(e[20]=[t("option",{value:"physical"},"Physical",-1),t("option",{value:"digital"},"Digital",-1)]),512),[[B,l.value.type]]),s.value.type?(a(),o("p",oe,d(s.value.type[0]),1)):i("",!0)]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Price *",-1)),c(t("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>l.value.price=r),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.price]]),s.value.price?(a(),o("p",de,d(s.value.price[0]),1)):i("",!0)]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Sale Price",-1)),c(t("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>l.value.sale_price=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.sale_price]]),s.value.sale_price?(a(),o("p",ue,d(s.value.sale_price[0]),1)):i("",!0)])]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Description *",-1)),c(t("textarea",{"onUpdate:modelValue":e[6]||(e[6]=r=>l.value.description=r),rows:"4",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.description]]),s.value.description?(a(),o("p",ie,d(s.value.description[0]),1)):i("",!0)]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Short Description",-1)),c(t("textarea",{"onUpdate:modelValue":e[7]||(e[7]=r=>l.value.short_description=r),rows:"2",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.short_description]]),s.value.short_description?(a(),o("p",ne,d(s.value.short_description[0]),1)):i("",!0)]),t("div",me,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100"},"Product Images",-1)),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Featured Image",-1)),t("div",ce,[t("div",ge,[t("input",{type:"file",name:"featured_image",accept:"image/*",onChange:j,class:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"},null,32),e[26]||(e[26]=t("p",{class:"mt-1 text-sm text-gray-500"},"PNG, JPG, GIF up to 10MB",-1)),s.value.featured_image?(a(),o("p",pe,d(s.value.featured_image[0]),1)):i("",!0)]),v.value?(a(),o("div",be,[t("img",{src:v.value,alt:"Featured image preview",class:"w-24 h-24 object-cover rounded-lg border border-gray-300"},null,8,ye),t("button",{type:"button",onClick:E,class:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"}," × ")])):i("",!0)])]),t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Gallery Images",-1)),t("div",fe,[t("input",{type:"file",multiple:"",accept:"image/*",onChange:M,class:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"},null,32),e[28]||(e[28]=t("p",{class:"text-sm text-gray-500"},"PNG, JPG, GIF up to 10MB each. You can select multiple images.",-1)),s.value.gallery_images?(a(),o("p",xe,d(s.value.gallery_images[0]),1)):i("",!0),k.value.length>0?(a(),o("div",ve,[(a(!0),o(_,null,U(k.value,(r,n)=>(a(),o("div",{key:n,class:"relative"},[t("img",{src:r.preview,alt:r.name,class:"w-full h-24 object-cover rounded-lg border border-gray-300"},null,8,ke),t("button",{type:"button",onClick:m=>A(n),class:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"}," × ",8,we),t("p",he,d(r.name),1)]))),128))])):i("",!0)])])]),l.value.type==="physical"?(a(),o("div",_e,[t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stock Quantity",-1)),c(t("input",{"onUpdate:modelValue":e[8]||(e[8]=r=>l.value.stock_quantity=r),type:"number",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.stock_quantity]]),s.value.stock_quantity?(a(),o("p",Ce,d(s.value.stock_quantity[0]),1)):i("",!0)]),t("div",null,[e[32]||(e[32]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Weight (kg)",-1)),c(t("input",{"onUpdate:modelValue":e[9]||(e[9]=r=>l.value.weight=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.weight]]),s.value.weight?(a(),o("p",Pe,d(s.value.weight[0]),1)):i("",!0)]),t("div",null,[e[33]||(e[33]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Dimensions",-1)),c(t("input",{"onUpdate:modelValue":e[10]||(e[10]=r=>l.value.dimensions=r),type:"text",placeholder:"L x W x H",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.dimensions]]),s.value.dimensions?(a(),o("p",Ve,d(s.value.dimensions[0]),1)):i("",!0)])])):i("",!0),t("div",Ue,[t("label",qe,[c(t("input",{"onUpdate:modelValue":e[11]||(e[11]=r=>l.value.is_featured=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[G,l.value.is_featured]]),e[34]||(e[34]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Featured Product",-1))]),t("label",Fe,[c(t("input",{"onUpdate:modelValue":e[12]||(e[12]=r=>l.value.is_active=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[G,l.value.is_active]]),e[35]||(e[35]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Active",-1))])]),t("div",Ie,[w(h(q),{href:"/admin/products",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:y(()=>e[36]||(e[36]=[F(" Cancel ")])),_:1}),t("button",{type:"submit",disabled:f.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},d(f.value?"Creating...":"Create Product"),9,De)])],32)])])])])]),_:1})],64))}};export{Ae as default};
