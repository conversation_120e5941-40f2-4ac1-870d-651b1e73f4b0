1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.medroid_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:42:5-47:15
24        <intent>
24-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:43:9-46:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:44:13-72
25-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:44:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
27-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:19-48
28        </intent>
29        <!-- Added to check the default browser that will host the AuthFlow. -->
30        <intent>
30-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:13:9-17:18
31            <action android:name="android.intent.action.VIEW" />
31-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:14:13-65
31-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:14:21-62
32
33            <data android:scheme="http" />
33-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
34        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
35        <package android:name="com.android.chrome" /> <!-- Needs to be explicitly declared on Android R+ -->
35-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:11:9-54
35-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:11:18-51
36        <package android:name="com.google.android.apps.maps" />
36-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db26a6a14b37d76dd687e7a530aa9f7f\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:33:9-64
36-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db26a6a14b37d76dd687e7a530aa9f7f\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:33:18-61
37    </queries>
38
39    <uses-permission android:name="android.permission.RECORD_AUDIO" />
39-->[:record_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-71
39-->[:record_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-68
40    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
40-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-75
40-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-72
41    <uses-permission android:name="android.permission.CAMERA" />
41-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-65
41-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-62
42    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
42-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
42-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-77
43    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
43-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-76
43-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-73
44    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- The Agora SDK requires Bluetooth permissions in case users are using Bluetooth devices. -->
44-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-79
44-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:22-76
45    <uses-permission android:name="android.permission.BLUETOOTH" /> <!-- For Android 12 and above devices, the following permission is also required. -->
45-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-68
45-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-65
46    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
46-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-76
46-->[:agora_rtc_engine] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:22-73
47    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
47-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
47-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
48    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
48-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
48-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
49    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
49-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
49-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
50
51    <uses-feature
51-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db26a6a14b37d76dd687e7a530aa9f7f\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:26:5-28:35
52        android:glEsVersion="0x00020000"
52-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db26a6a14b37d76dd687e7a530aa9f7f\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:27:9-41
53        android:required="true" />
53-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db26a6a14b37d76dd687e7a530aa9f7f\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:28:9-32
54
55    <permission
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
56        android:name="com.example.medroid_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
57        android:protectionLevel="signature" />
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
58
59    <uses-permission android:name="com.example.medroid_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
60    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
60-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:5-77
60-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:22-74
61    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
61-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:5-94
61-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:22-91
62
63    <application
64        android:name="android.app.Application"
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
66        android:debuggable="true"
67        android:extractNativeLibs="false"
68        android:icon="@mipmap/launcher_icon"
69        android:label="Medroid" >
70        <activity
71            android:name="com.example.medroid_app.MainActivity"
72            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
73            android:exported="true"
74            android:hardwareAccelerated="true"
75            android:launchMode="singleTop"
76            android:taskAffinity=""
77            android:theme="@style/LaunchTheme"
78            android:windowSoftInputMode="adjustResize" >
79
80            <!--
81                 Specifies an Android theme to apply to this Activity as soon as
82                 the Android process has started. This theme is visible to the user
83                 while the Flutter UI initializes. After that, this theme continues
84                 to determine the Window background behind the Flutter UI.
85            -->
86            <meta-data
87                android:name="io.flutter.embedding.android.NormalTheme"
88                android:resource="@style/NormalTheme" />
89
90            <intent-filter>
91                <action android:name="android.intent.action.MAIN" />
92
93                <category android:name="android.intent.category.LAUNCHER" />
94            </intent-filter>
95        </activity>
96        <!--
97             Don't delete the meta-data below.
98             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
99        -->
100        <meta-data
101            android:name="flutterEmbedding"
102            android:value="2" />
103
104        <activity
104-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:21:9-65:20
105            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
105-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:22:13-109
106            android:exported="true"
106-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:23:13-36
107            android:launchMode="singleTask" >
107-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:24:13-44
108            <intent-filter>
108-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:25:13-64:29
109                <action android:name="android.intent.action.VIEW" />
109-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:14:13-65
109-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:14:21-62
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:28:17-76
111-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:28:27-73
112                <category android:name="android.intent.category.BROWSABLE" />
112-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:29:17-78
112-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:29:27-75
113
114                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
115                <data
115-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
116                    android:host="link-accounts"
117                    android:pathPrefix="/com.example.medroid_app/authentication_return"
118                    android:scheme="stripe-auth" />
119
120                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
121                <data
121-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
122                    android:host="link-native-accounts"
123                    android:pathPrefix="/com.example.medroid_app/authentication_return"
124                    android:scheme="stripe-auth" />
125
126                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
127                <data
127-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
128                    android:host="link-accounts"
129                    android:path="/com.example.medroid_app/success"
130                    android:scheme="stripe-auth" />
131                <data
131-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
132                    android:host="link-accounts"
133                    android:path="/com.example.medroid_app/cancel"
134                    android:scheme="stripe-auth" />
135
136                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
137                <data
137-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
138                    android:host="native-redirect"
139                    android:pathPrefix="/com.example.medroid_app"
140                    android:scheme="stripe-auth" />
141
142                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
143                <data
143-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
144                    android:host="auth-redirect"
145                    android:pathPrefix="/com.example.medroid_app"
146                    android:scheme="stripe" />
147            </intent-filter>
148        </activity>
149        <activity
149-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:66:9-69:57
150            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
150-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:67:13-101
151            android:exported="false"
151-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:68:13-37
152            android:theme="@style/StripeDefaultTheme" />
152-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:69:13-54
153        <activity
153-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:70:9-74:58
154            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
154-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:71:13-110
155            android:exported="false"
155-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:72:13-37
156            android:theme="@style/StripeDefaultTheme"
156-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:73:13-54
157            android:windowSoftInputMode="adjustResize" />
157-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:74:13-55
158
159        <provider
159-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:76:9-80:43
160            android:name="com.stripe.android.financialconnections.appinitializer.FinancialConnectionsInitializer"
160-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:77:13-114
161            android:authorities="com.example.medroid_app.financialconnections-init"
161-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:78:13-77
162            android:exported="false"
162-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:79:13-37
163            android:multiprocess="true" />
163-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:80:13-40
164
165        <service
165-->[:geolocator_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
166            android:name="com.baseflow.geolocator.GeolocatorLocationService"
166-->[:geolocator_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
167            android:enabled="true"
167-->[:geolocator_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
168            android:exported="false"
168-->[:geolocator_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
169            android:foregroundServiceType="location" />
169-->[:geolocator_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
170        <service
170-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
171            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
171-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
172            android:exported="false"
172-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
173            android:permission="android.permission.BIND_JOB_SERVICE" />
173-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
174        <service
174-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
175            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
175-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
176            android:exported="false" >
176-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
177            <intent-filter>
177-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
178                <action android:name="com.google.firebase.MESSAGING_EVENT" />
178-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
178-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
179            </intent-filter>
180        </service>
181
182        <receiver
182-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
183            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
183-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
184            android:exported="true"
184-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
185            android:permission="com.google.android.c2dm.permission.SEND" >
185-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
186            <intent-filter>
186-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
187                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
187-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
187-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
188            </intent-filter>
189        </receiver>
190
191        <service
191-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
192            android:name="com.google.firebase.components.ComponentDiscoveryService"
192-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
193            android:directBootAware="true"
193-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
194            android:exported="false" >
194-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
195            <meta-data
195-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
196                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
196-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
197                android:value="com.google.firebase.components.ComponentRegistrar" />
197-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
198            <meta-data
198-->[:firebase_core] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
199                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
199-->[:firebase_core] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
200                android:value="com.google.firebase.components.ComponentRegistrar" />
200-->[:firebase_core] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
201            <meta-data
201-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
202                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
202-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
203                android:value="com.google.firebase.components.ComponentRegistrar" />
203-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
204            <meta-data
204-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
205                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
205-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
207            <meta-data
207-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
208                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
208-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
210            <meta-data
210-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
211                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
211-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
213            <meta-data
213-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d01334e5efd132dc1afb0484ccb5aa\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
214                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
214-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d01334e5efd132dc1afb0484ccb5aa\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d01334e5efd132dc1afb0484ccb5aa\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
216            <meta-data
216-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
217                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
217-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
219            <meta-data
219-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
220                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
220-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
222        </service>
223
224        <provider
224-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
225            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
225-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
226            android:authorities="com.example.medroid_app.flutterfirebasemessaginginitprovider"
226-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
227            android:exported="false"
227-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
228            android:initOrder="99" />
228-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
229        <provider
229-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
230            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
230-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
231            android:authorities="com.example.medroid_app.flutter.image_provider"
231-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
232            android:exported="false"
232-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
233            android:grantUriPermissions="true" >
233-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
234            <meta-data
234-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
235                android:name="android.support.FILE_PROVIDER_PATHS"
235-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
236                android:resource="@xml/flutter_image_picker_file_paths" />
236-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
237        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
238        <service
238-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
239            android:name="com.google.android.gms.metadata.ModuleDependencies"
239-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
240            android:enabled="false"
240-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
241            android:exported="false" >
241-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
242            <intent-filter>
242-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
243                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
243-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
243-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
244            </intent-filter>
245
246            <meta-data
246-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
247                android:name="photopicker_activity:0:required"
247-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
248                android:value="" />
248-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
249        </service>
250        <!--
251           Declares a provider which allows us to store files to share in
252           '.../caches/share_plus' and grant the receiving action access
253        -->
254        <provider
254-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
255            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
255-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
256            android:authorities="com.example.medroid_app.flutter.share_provider"
256-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
257            android:exported="false"
257-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
258            android:grantUriPermissions="true" >
258-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
259            <meta-data
259-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
260                android:name="android.support.FILE_PROVIDER_PATHS"
260-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
261                android:resource="@xml/flutter_share_file_paths" />
261-->[:image_picker_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
262        </provider>
263        <!--
264           This manifest declared broadcast receiver allows us to use an explicit
265           Intent when creating a PendingItent to be informed of the user's choice
266        -->
267        <receiver
267-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
268            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
268-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
269            android:exported="false" >
269-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
270            <intent-filter>
270-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
271                <action android:name="EXTRA_CHOSEN_COMPONENT" />
271-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
271-->[:share_plus] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
272            </intent-filter>
273        </receiver>
274
275        <activity
275-->[:url_launcher_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
276            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
276-->[:url_launcher_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
277            android:exported="false"
277-->[:url_launcher_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
278            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
278-->[:url_launcher_android] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
279        <activity
279-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
280            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
280-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
281            android:excludeFromRecents="true"
281-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
282            android:exported="false"
282-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
283            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
283-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
284        <!--
285            Service handling Google Sign-In user revocation. For apps that do not integrate with
286            Google Sign-In, this service will never be started.
287        -->
288        <service
288-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
289            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
289-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
290            android:exported="true"
290-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
291            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
291-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
292            android:visibleToInstantApps="true" />
292-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
293
294        <receiver
294-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
295            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
295-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
296            android:exported="true"
296-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
297            android:permission="com.google.android.c2dm.permission.SEND" >
297-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
298            <intent-filter>
298-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
299                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
299-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
299-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
300            </intent-filter>
301
302            <meta-data
302-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
303                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
303-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
304                android:value="true" />
304-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
305        </receiver>
306        <!--
307             FirebaseMessagingService performs security checks at runtime,
308             but set to not exported to explicitly avoid allowing another app to call it.
309        -->
310        <service
310-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
311            android:name="com.google.firebase.messaging.FirebaseMessagingService"
311-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
312            android:directBootAware="true"
312-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
313            android:exported="false" >
313-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
314            <intent-filter android:priority="-500" >
314-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
315                <action android:name="com.google.firebase.MESSAGING_EVENT" />
315-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
315-->[:firebase_messaging] C:\laragon\www\medroid-app\medroid-full\medroid-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
316            </intent-filter>
317        </service>
318
319        <activity
319-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:8:9-12:58
320            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
320-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:9:13-80
321            android:exported="false"
321-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:10:13-37
322            android:theme="@style/StripePaymentSheetDefaultTheme"
322-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:11:13-66
323            android:windowSoftInputMode="adjustResize" />
323-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:12:13-55
324        <activity
324-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:13:9-17:58
325            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
325-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:14:13-82
326            android:exported="false"
326-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:15:13-37
327            android:theme="@style/StripePaymentSheetDefaultTheme"
327-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:16:13-66
328            android:windowSoftInputMode="adjustResize" />
328-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:17:13-55
329        <activity
329-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:18:9-22:58
330            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
330-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:19:13-82
331            android:exported="false"
331-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:20:13-37
332            android:theme="@style/StripePaymentSheetDefaultTheme"
332-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:21:13-66
333            android:windowSoftInputMode="adjustResize" />
333-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:22:13-55
334        <activity
334-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:23:9-26:69
335            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
335-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:24:13-97
336            android:exported="false"
336-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:25:13-37
337            android:theme="@style/StripePaymentSheetDefaultTheme" />
337-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:26:13-66
338        <activity
338-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:27:9-30:69
339            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
339-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:28:13-118
340            android:exported="false"
340-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:29:13-37
341            android:theme="@style/StripePaymentSheetDefaultTheme" />
341-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:30:13-66
342        <activity
342-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:31:9-34:69
343            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
343-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:32:13-105
344            android:exported="false"
344-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:33:13-37
345            android:theme="@style/StripePaymentSheetDefaultTheme" />
345-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:34:13-66
346        <activity
346-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:35:9-38:69
347            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
347-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:36:13-82
348            android:exported="false"
348-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:37:13-37
349            android:theme="@style/StripePaymentSheetDefaultTheme" />
349-->[com.stripe:paymentsheet:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71634029c28cc8731cc9cadb4b93d05\transformed\jetified-paymentsheet-20.34.4\AndroidManifest.xml:38:13-66
350        <activity
350-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:8:9-13:61
351            android:name="com.stripe.android.link.LinkForegroundActivity"
351-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:9:13-74
352            android:autoRemoveFromRecents="true"
352-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:10:13-49
353            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
353-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:11:13-115
354            android:launchMode="singleTop"
354-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:12:13-43
355            android:theme="@style/StripeTransparentTheme" />
355-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:13:13-58
356        <activity
356-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:14:9-31:20
357            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
357-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:15:13-79
358            android:autoRemoveFromRecents="true"
358-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:16:13-49
359            android:exported="true"
359-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:17:13-36
360            android:launchMode="singleInstance"
360-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:18:13-48
361            android:theme="@style/StripeTransparentTheme" >
361-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:19:13-58
362            <intent-filter>
362-->[com.stripe:link:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd497b801a534c8e6518183be54f249b\transformed\jetified-link-20.34.4\AndroidManifest.xml:20:13-30:29
363                <action android:name="android.intent.action.VIEW" />
363-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:14:13-65
363-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:14:21-62
364
365                <category android:name="android.intent.category.DEFAULT" />
365-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:28:17-76
365-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:28:27-73
366                <category android:name="android.intent.category.BROWSABLE" />
366-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:29:17-78
366-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:29:27-75
367
368                <data
368-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
369                    android:host="complete"
370                    android:path="/com.example.medroid_app"
371                    android:scheme="link-popup" />
372            </intent-filter>
373        </activity>
374        <activity
374-->[com.stripe:payments-ui-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de6d39d460bdc21c935fb84db93695ba\transformed\jetified-payments-ui-core-20.34.4\AndroidManifest.xml:8:9-11:69
375            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
375-->[com.stripe:payments-ui-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de6d39d460bdc21c935fb84db93695ba\transformed\jetified-payments-ui-core-20.34.4\AndroidManifest.xml:9:13-80
376            android:exported="false"
376-->[com.stripe:payments-ui-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de6d39d460bdc21c935fb84db93695ba\transformed\jetified-payments-ui-core-20.34.4\AndroidManifest.xml:10:13-37
377            android:theme="@style/StripePaymentSheetDefaultTheme" />
377-->[com.stripe:payments-ui-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de6d39d460bdc21c935fb84db93695ba\transformed\jetified-payments-ui-core-20.34.4\AndroidManifest.xml:11:13-66
378        <activity
378-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:15:9-18:57
379            android:name="com.stripe.android.view.AddPaymentMethodActivity"
379-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:16:13-76
380            android:exported="false"
380-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:17:13-37
381            android:theme="@style/StripeDefaultTheme" />
381-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:18:13-54
382        <activity
382-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:19:9-22:57
383            android:name="com.stripe.android.view.PaymentMethodsActivity"
383-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:20:13-74
384            android:exported="false"
384-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:21:13-37
385            android:theme="@style/StripeDefaultTheme" />
385-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:22:13-54
386        <activity
386-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:23:9-26:57
387            android:name="com.stripe.android.view.PaymentFlowActivity"
387-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:24:13-71
388            android:exported="false"
388-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:25:13-37
389            android:theme="@style/StripeDefaultTheme" />
389-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:26:13-54
390        <activity
390-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:27:9-31:58
391            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
391-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:28:13-78
392            android:exported="false"
392-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:29:13-37
393            android:theme="@style/StripeDefaultTheme"
393-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:30:13-54
394            android:windowSoftInputMode="adjustResize" />
394-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:31:13-55
395        <activity
395-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:32:9-35:61
396            android:name="com.stripe.android.view.PaymentRelayActivity"
396-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:33:13-72
397            android:exported="false"
397-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:34:13-37
398            android:theme="@style/StripeTransparentTheme" />
398-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:35:13-58
399        <!--
400        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
401        launched the browser Activity will also handle the return URL deep link.
402        -->
403        <activity
403-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:41:9-45:61
404            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
404-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:42:13-85
405            android:exported="false"
405-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:43:13-37
406            android:launchMode="singleTask"
406-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:44:13-44
407            android:theme="@style/StripeTransparentTheme" />
407-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:45:13-58
408        <activity
408-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:46:9-63:20
409            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
409-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:47:13-88
410            android:exported="true"
410-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:48:13-36
411            android:launchMode="singleTask"
411-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:49:13-44
412            android:theme="@style/StripeTransparentTheme" >
412-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:50:13-58
413            <intent-filter>
413-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:51:13-62:29
414                <action android:name="android.intent.action.VIEW" />
414-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:14:13-65
414-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:14:21-62
415
416                <category android:name="android.intent.category.DEFAULT" />
416-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:28:17-76
416-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:28:27-73
417                <category android:name="android.intent.category.BROWSABLE" />
417-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:29:17-78
417-->[com.stripe:financial-connections:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c725f179bcf118f2edcb2b18ab0533\transformed\jetified-financial-connections-20.34.4\AndroidManifest.xml:29:27-75
418
419                <!-- Must match `DefaultReturnUrl#value`. -->
420                <data
420-->C:\laragon\www\medroid-app\medroid-full\medroid-app\android\app\src\main\AndroidManifest.xml:45:13-50
421                    android:host="payment_return_url"
422                    android:path="/com.example.medroid_app"
423                    android:scheme="stripesdk" />
424            </intent-filter>
425        </activity>
426        <activity
426-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:64:9-67:57
427            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
427-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:65:13-114
428            android:exported="false"
428-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:66:13-37
429            android:theme="@style/StripeDefaultTheme" />
429-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:67:13-54
430        <activity
430-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:68:9-71:66
431            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
431-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:69:13-90
432            android:exported="false"
432-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:70:13-37
433            android:theme="@style/StripeGooglePayDefaultTheme" />
433-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:71:13-63
434        <activity
434-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:72:9-75:66
435            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
435-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:73:13-103
436            android:exported="false"
436-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:74:13-37
437            android:theme="@style/StripeGooglePayDefaultTheme" />
437-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:75:13-63
438        <activity
438-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:76:9-79:68
439            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
439-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:77:13-107
440            android:exported="false"
440-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:78:13-37
441            android:theme="@style/StripePayLauncherDefaultTheme" />
441-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:79:13-65
442        <activity
442-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:80:9-83:61
443            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
443-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:81:13-97
444            android:exported="false"
444-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:82:13-37
445            android:theme="@style/StripeTransparentTheme" /> <!-- Needs to be explicitly declared on P+ -->
445-->[com.stripe:payments-core:20.34.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\635aae3c1d96b84c5b5820d0f3468bfb\transformed\jetified-payments-core-20.34.4\AndroidManifest.xml:83:13-58
446        <uses-library
446-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db26a6a14b37d76dd687e7a530aa9f7f\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:39:9-41:40
447            android:name="org.apache.http.legacy"
447-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db26a6a14b37d76dd687e7a530aa9f7f\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:40:13-50
448            android:required="false" />
448-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db26a6a14b37d76dd687e7a530aa9f7f\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:41:13-37
449
450        <activity
450-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
451            android:name="com.google.android.gms.common.api.GoogleApiActivity"
451-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
452            android:exported="false"
452-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
453            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
453-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
454
455        <provider
455-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
456            android:name="com.google.firebase.provider.FirebaseInitProvider"
456-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
457            android:authorities="com.example.medroid_app.firebaseinitprovider"
457-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
458            android:directBootAware="true"
458-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
459            android:exported="false"
459-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
460            android:initOrder="100" />
460-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7753fbb9eb20d93e79e4caf23f22802c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
461
462        <uses-library
462-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
463            android:name="androidx.window.extensions"
463-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
464            android:required="false" />
464-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
465        <uses-library
465-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
466            android:name="androidx.window.sidecar"
466-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
467            android:required="false" />
467-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
468
469        <activity
469-->[com.stripe:stripe-3ds2-android:6.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f779a58ef2338aeb71ccad028cde32d\transformed\jetified-stripe-3ds2-android-6.1.7\AndroidManifest.xml:8:9-11:54
470            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
470-->[com.stripe:stripe-3ds2-android:6.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f779a58ef2338aeb71ccad028cde32d\transformed\jetified-stripe-3ds2-android-6.1.7\AndroidManifest.xml:9:13-81
471            android:exported="false"
471-->[com.stripe:stripe-3ds2-android:6.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f779a58ef2338aeb71ccad028cde32d\transformed\jetified-stripe-3ds2-android-6.1.7\AndroidManifest.xml:10:13-37
472            android:theme="@style/Stripe3DS2Theme" />
472-->[com.stripe:stripe-3ds2-android:6.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f779a58ef2338aeb71ccad028cde32d\transformed\jetified-stripe-3ds2-android-6.1.7\AndroidManifest.xml:11:13-51
473
474        <meta-data
474-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
475            android:name="com.google.android.gms.version"
475-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
476            android:value="@integer/google_play_services_version" />
476-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
477
478        <provider
478-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
479            android:name="androidx.startup.InitializationProvider"
479-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
480            android:authorities="com.example.medroid_app.androidx-startup"
480-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
481            android:exported="false" >
481-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
482            <meta-data
482-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
483                android:name="androidx.emoji2.text.EmojiCompatInitializer"
483-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
484                android:value="androidx.startup" />
484-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
485            <meta-data
485-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
486                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
486-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
487                android:value="androidx.startup" />
487-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
488            <meta-data
488-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
489                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
489-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
490                android:value="androidx.startup" />
490-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
491        </provider>
492
493        <receiver
493-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
494            android:name="androidx.profileinstaller.ProfileInstallReceiver"
494-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
495            android:directBootAware="false"
495-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
496            android:enabled="true"
496-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
497            android:exported="true"
497-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
498            android:permission="android.permission.DUMP" >
498-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
499            <intent-filter>
499-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
500                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
500-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
500-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
501            </intent-filter>
502            <intent-filter>
502-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
503                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
503-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
503-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
504            </intent-filter>
505            <intent-filter>
505-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
506                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
506-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
506-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
507            </intent-filter>
508            <intent-filter>
508-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
509                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
509-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
509-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
510            </intent-filter>
511        </receiver>
512
513        <service
513-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
514            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
514-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
515            android:exported="false" >
515-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
516            <meta-data
516-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
517                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
517-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
518                android:value="cct" />
518-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
519        </service>
520        <service
520-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
521            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
521-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
522            android:exported="false"
522-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
523            android:permission="android.permission.BIND_JOB_SERVICE" >
523-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
524        </service>
525
526        <receiver
526-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
527            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
527-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
528            android:exported="false" />
528-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
529
530        <activity
530-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:9-14:63
531            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenCaptureAssistantActivity"
531-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:19-89
532            android:configChanges="screenSize|orientation"
532-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:12:13-59
533            android:screenOrientation="unspecified"
533-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:13:13-52
534            android:theme="@android:style/Theme.Translucent" />
534-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:14:13-61
535
536        <service
536-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:16:9-19:19
537            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenSharingService"
537-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:17:13-73
538            android:foregroundServiceType="mediaProjection" >
538-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:18:13-60
539        </service>
540
541        <meta-data
541-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c7790ab43a5e01a85a18cdd7cfc5816\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
542            android:name="aia-compat-api-min-version"
542-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c7790ab43a5e01a85a18cdd7cfc5816\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
543            android:value="1" />
543-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c7790ab43a5e01a85a18cdd7cfc5816\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
544    </application>
545
546</manifest>
