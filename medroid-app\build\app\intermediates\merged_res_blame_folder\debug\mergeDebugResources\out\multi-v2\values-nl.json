{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4769,4877,5028,5156,5267,5434,5561,5684,5933,6111,6217,6386,6512,6675,6857,6925,6988", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "4872,5023,5151,5262,5429,5556,5679,5785,6106,6212,6381,6507,6670,6852,6920,6983,7062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba2bbf823ab5a18be680588e75f7e6ba\\transformed\\jetified-play-services-wallet-19.2.1\\res\\values-nl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "91,422", "startColumns": "4,4", "startOffsets": "8892,39489", "endColumns": "60,75", "endOffsets": "8948,39560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,249,386,487,577,705,905,1124,1280,1372,1473,1566,1660,1755,1817,1884,1980,2082,2290,2366,2456,2528,2598,2714,2801,2900,2973,3034,3141,3250,3384,3479,3551,3628,3724,3798,3909,4027", "endColumns": "92,100,136,100,89,127,199,218,155,91,100,92,93,94,61,66,95,101,207,75,89,71,69,115,86,98,72,60,106,108,133,94,71,76,95,73,110,117,87", "endOffsets": "143,244,381,482,572,700,900,1119,1275,1367,1468,1561,1655,1750,1812,1879,1975,2077,2285,2361,2451,2523,2593,2709,2796,2895,2968,3029,3136,3245,3379,3474,3546,3623,3719,3793,3904,4022,4110"}, "to": {"startLines": "172,250,267,277,278,325,331,332,333,334,336,337,338,340,341,342,343,344,345,346,347,348,349,350,355,356,357,358,359,360,361,362,363,381,388,389,390,391,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15261,22207,24287,25315,25416,29376,29870,30070,30289,30445,30635,30736,30829,31009,31104,31166,31233,31329,31431,31639,31715,31805,31877,31947,32401,32488,32587,32660,32721,32828,32937,33071,33166,35630,36139,36235,36309,36420,36538", "endColumns": "92,100,136,100,89,127,199,218,155,91,100,92,93,94,61,66,95,101,207,75,89,71,69,115,86,98,72,60,106,108,133,94,71,76,95,73,110,117,87", "endOffsets": "15349,22303,24419,25411,25501,29499,30065,30284,30440,30532,30731,30824,30918,31099,31161,31228,31324,31426,31634,31710,31800,31872,31942,32058,32483,32582,32655,32716,32823,32932,33066,33161,33233,35702,36230,36304,36415,36533,36621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,261,331,430,502,770,830,919,983,1038,1102,1439,1510,1576,1629,1682,1774,1907,2026,2083,2167,2246,2328,2395,2491,2568,2646,2714,2774,2838,2898,2996,3082,3180,3254,3333,3420,3644,3876,3994,4059,4798,4862", "endColumns": "159,45,69,98,71,267,59,88,63,54,63,336,70,65,52,52,91,132,118,56,83,78,81,66,95,76,77,67,59,63,59,97,85,97,73,78,86,223,231,117,64,738,63,54", "endOffsets": "210,256,326,425,497,765,825,914,978,1033,1097,1434,1505,1571,1624,1677,1769,1902,2021,2078,2162,2241,2323,2390,2486,2563,2641,2709,2769,2833,2893,2991,3077,3175,3249,3328,3415,3639,3871,3989,4054,4793,4857,4912"}, "to": {"startLines": "239,243,244,245,246,247,248,249,263,266,268,274,279,280,287,297,300,301,302,303,304,310,313,318,320,321,322,324,326,327,330,335,339,351,352,353,354,364,370,371,372,374,375,387", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20444,21503,21549,21619,21718,21790,22058,22118,23978,24232,24424,24878,25506,25577,26052,26987,27187,27279,27412,27531,27588,28163,28419,28855,28996,29092,29169,29308,29504,29564,29810,30537,30923,32063,32161,32235,32314,33238,33856,34088,34206,34346,35085,36084", "endColumns": "159,45,69,98,71,267,59,88,63,54,63,336,70,65,52,52,91,132,118,56,83,78,81,66,95,76,77,67,59,63,59,97,85,97,73,78,86,223,231,117,64,738,63,54", "endOffsets": "20599,21544,21614,21713,21785,22053,22113,22202,24037,24282,24483,25210,25572,25638,26100,27035,27274,27407,27526,27583,27667,28237,28496,28917,29087,29164,29242,29371,29559,29623,29865,30630,31004,32156,32230,32309,32396,33457,34083,34201,34266,35080,35144,36134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5790", "endColumns": "142", "endOffsets": "5928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,194,262,346,417,478,550,617,680,748,818,883,946,1020,1084,1151,1214,1290,1355,1441,1518,1598,1684,1791,1874,1925,1978,2058,2122,2187,2257,2358,2443,2540", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "113,189,257,341,412,473,545,612,675,743,813,878,941,1015,1079,1146,1209,1285,1350,1436,1513,1593,1679,1786,1869,1920,1973,2053,2117,2182,2252,2353,2438,2535,2631"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,264,265,275,285,286,288,295,296,305,306,314,315", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15727,15963,16221,16368,16452,16523,17025,17097,17238,17301,17369,17439,17504,17567,17641,17764,17831,18069,18954,19019,20016,20168,20248,24042,24149,25215,25919,25972,26105,26852,26917,27672,27773,28501,28598", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "15785,16034,16284,16447,16518,16579,17092,17159,17296,17364,17434,17499,17562,17636,17700,17826,17889,18140,19014,19100,20088,20243,20329,24144,24227,25261,25967,26047,26164,26912,26982,27768,27853,28593,28689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7667,7738,7802,7866,7933,8010,8079,8168,8251", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "7733,7797,7861,7928,8005,8074,8163,8246,8318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "69,85,147,151,416,420,421", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7067,8323,13299,13591,38889,39332,39412", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "7134,8408,13375,13732,39053,39407,39484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,246,313,380,447,524", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "139,241,308,375,442,519,596"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14162,14251,14353,14420,14487,14554,14631", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "14246,14348,14415,14482,14549,14626,14703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1070,1159,1238,1301,1394,1456,1522,1580,1653,1717,1773,1895,1952,2014,2070,2146,2280,2365,2451,2589,2670,2749,2839,2896,2947,3013,3091,3174,3262,3337,3416,3489,3560,3654,3732,3821,3911,3985,4066,4153,4206,4273,4354,4438,4500,4564,4627,4735,4836,4938,5041,5102,5157", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,89,56,50,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,60,54,80", "endOffsets": "264,350,432,509,607,701,798,920,1001,1065,1154,1233,1296,1389,1451,1517,1575,1648,1712,1768,1890,1947,2009,2065,2141,2275,2360,2446,2584,2665,2744,2834,2891,2942,3008,3086,3169,3257,3332,3411,3484,3555,3649,3727,3816,3906,3980,4061,4148,4201,4268,4349,4433,4495,4559,4622,4730,4831,4933,5036,5097,5152,5233"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,3384,4212,4309,4431,7603,8492,8953,9204,9267,9360,9422,9488,9546,9619,9683,9739,9861,9918,9980,10036,10112,10246,10331,10417,10555,10636,10715,10805,10862,10913,10979,11057,11140,11228,11303,11382,11455,11526,11620,11698,11787,11877,11951,12032,12119,12172,12239,12320,12404,12466,12530,12593,12701,12802,12904,13007,13068,13510", "endLines": "5,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,89,56,50,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,60,54,80", "endOffsets": "314,3122,3204,3281,3379,3473,4304,4426,4507,7662,8576,9027,9262,9355,9417,9483,9541,9614,9678,9734,9856,9913,9975,10031,10107,10241,10326,10412,10550,10631,10710,10800,10857,10908,10974,11052,11135,11223,11298,11377,11450,11521,11615,11693,11782,11872,11946,12027,12114,12167,12234,12315,12399,12461,12525,12588,12696,12797,12899,13002,13063,13118,13586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,533,588,663,743,824,921,1018,1103,1182,1264,1352,1450,1541,1608,1696,1785,1871,1978,2060,2148,2222,2317,2390,2481,2569,2653,2735,2835,2904,2979,3089,3161,3226,3988,4726,4803,4920,5021,5076,5179,5277,5342,5431,5520,5577,5658,5710,5790,5904,5975,6048,6097,6180,6277,6324,6373,6443,6501,6567,6759,6926,7056,7121,7203,7288,7388,7472,7565,7640,7726,7800,7887,7982,8035,8175,8229,8286,8361,8433,8508,8575,8645,8738", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,48,82,96,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,86,94,52,139,53,56,74,71,74,66,69,92,77", "endOffsets": "119,197,259,328,406,464,528,583,658,738,819,916,1013,1098,1177,1259,1347,1445,1536,1603,1691,1780,1866,1973,2055,2143,2217,2312,2385,2476,2564,2648,2730,2830,2899,2974,3084,3156,3221,3983,4721,4798,4915,5016,5071,5174,5272,5337,5426,5515,5572,5653,5705,5785,5899,5970,6043,6092,6175,6272,6319,6368,6438,6496,6562,6754,6921,7051,7116,7198,7283,7383,7467,7560,7635,7721,7795,7882,7977,8030,8170,8224,8281,8356,8428,8503,8570,8640,8733,8811"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,240,241,242,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,272,273,276,281,282,283,284,289,290,291,292,293,294,298,299,308,309,311,312,316,317,319,328,329,366,367,368,369,373,382,383,384,385,386,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14708,14777,14855,14917,14986,15064,15122,15436,15491,15566,15646,15866,16039,16136,16289,16645,16937,17971,18215,18306,18373,18529,18618,18704,18872,19172,19260,19334,19429,19502,19593,19681,19765,19847,19947,20093,20334,20604,20676,20741,22308,23046,23123,23240,23341,23396,23499,23597,23662,23751,23840,23897,24488,24540,24620,24734,24805,25266,25643,25726,25823,25870,26169,26239,26297,26363,26555,26722,27040,27105,27978,28063,28242,28326,28694,28769,28922,29628,29715,33552,33605,33745,33799,34271,35707,35779,35854,35921,35991,37353", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,48,82,96,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,86,94,52,139,53,56,74,71,74,66,69,92,77", "endOffsets": "14772,14850,14912,14981,15059,15117,15181,15486,15561,15641,15722,15958,16131,16216,16363,16722,17020,18064,18301,18368,18456,18613,18699,18806,18949,19255,19329,19424,19497,19588,19676,19760,19842,19942,20011,20163,20439,20671,20736,21498,23041,23118,23235,23336,23391,23494,23592,23657,23746,23835,23892,23973,24535,24615,24729,24800,24873,25310,25721,25818,25865,25914,26234,26292,26358,26550,26717,26847,27100,27182,28058,28158,28321,28414,28764,28850,28991,29710,29805,33600,33740,33794,33851,34341,35774,35849,35916,35986,36079,37426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be80f888898887bb71616e48bf7874ba\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "48,49,71,72,74,93,94,145,146,148,149,152,153,155,417,418,419", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4512,4604,7242,7339,7518,9032,9108,13123,13210,13380,13445,13737,13818,13984,39058,39142,39212", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4599,4682,7334,7433,7598,9103,9199,13205,13294,13440,13505,13813,13896,14056,39137,39207,39327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e71e7c08b297cb32539b89f832f53d8\\transformed\\jetified-material3-1.0.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,217", "endColumns": "81,79,78", "endOffsets": "132,212,291"}, "to": {"startLines": "50,73,86", "startColumns": "4,4,4", "startOffsets": "4687,7438,8413", "endColumns": "81,79,78", "endOffsets": "4764,7513,8487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,192,254,335,402,476,535,612,682,750,811", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "126,187,249,330,397,471,530,607,677,745,806,873"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15790,16584,16727,16789,16870,17164,17705,17894,18145,18461,18811,19105", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "15861,16640,16784,16865,16932,17233,17759,17966,18210,18524,18867,19167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3478,3580,3682,3782,3882,3989,4093,14061", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3575,3677,3777,3877,3984,4088,4207,14157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,13901", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,13979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "70,88,89,90", "startColumns": "4,4,4,4", "startOffsets": "7139,8581,8682,8793", "endColumns": "102,100,110,98", "endOffsets": "7237,8677,8788,8887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,212,332,393,483,543,605,684,791,964,1046,1119,1202,1294,1415,1546,1613,1691,1884,1953,2013,2085,2172,2318,2450,2528,2651,2745,2826,2915,2991", "endColumns": "74,81,119,60,89,59,61,78,106,172,81,72,82,91,120,130,66,77,192,68,59,71,86,145,131,77,122,93,80,88,75,157", "endOffsets": "125,207,327,388,478,538,600,679,786,959,1041,1114,1197,1289,1410,1541,1608,1686,1879,1948,2008,2080,2167,2313,2445,2523,2646,2740,2821,2910,2986,3144"}, "to": {"startLines": "171,173,307,323,365,376,377,378,379,380,393,394,395,396,397,398,399,400,402,403,404,405,406,407,408,409,410,411,412,413,414,415", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15186,15354,27858,29247,33462,35149,35209,35271,35350,35457,36626,36708,36781,36864,36956,37077,37208,37275,37431,37624,37693,37753,37825,37912,38058,38190,38268,38391,38485,38566,38655,38731", "endColumns": "74,81,119,60,89,59,61,78,106,172,81,72,82,91,120,130,66,77,192,68,59,71,86,145,131,77,122,93,80,88,75,157", "endOffsets": "15256,15431,27973,29303,33547,35204,35266,35345,35452,35625,36703,36776,36859,36951,37072,37203,37270,37348,37619,37688,37748,37820,37907,38053,38185,38263,38386,38480,38561,38650,38726,38884"}}]}]}