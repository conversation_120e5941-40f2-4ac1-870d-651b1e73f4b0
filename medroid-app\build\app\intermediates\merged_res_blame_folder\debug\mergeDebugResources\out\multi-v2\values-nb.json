{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,13680", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,13776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,465,524,577,653,730,811,910,1009,1095,1175,1253,1336,1433,1526,1595,1685,1772,1860,1969,2050,2140,2216,2313,2392,2488,2576,2665,2750,2851,2926,2999,3103,3175,3240,3917,4569,4643,4758,4853,4908,5001,5086,5153,5241,5329,5386,5464,5513,5590,5696,5768,5843,5889,5968,6060,6107,6155,6226,6284,6344,6524,6686,6810,6877,6961,7039,7136,7216,7298,7371,7460,7537,7621,7706,7759,7891,7939,7993,8062,8128,8197,8261,8331,8419", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,45,78,91,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,83,84,52,131,47,53,68,65,68,63,69,87,79", "endOffsets": "118,195,257,325,402,460,519,572,648,725,806,905,1004,1090,1170,1248,1331,1428,1521,1590,1680,1767,1855,1964,2045,2135,2211,2308,2387,2483,2571,2660,2745,2846,2921,2994,3098,3170,3235,3912,4564,4638,4753,4848,4903,4996,5081,5148,5236,5324,5381,5459,5508,5585,5691,5763,5838,5884,5963,6055,6102,6150,6221,6279,6339,6519,6681,6805,6872,6956,7034,7131,7211,7293,7366,7455,7532,7616,7701,7754,7886,7934,7988,8057,8123,8192,8256,8326,8414,8494"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,233,234,235,244,245,246,247,248,249,250,251,252,253,254,255,262,263,264,265,266,269,274,275,276,277,282,283,284,285,286,287,291,292,301,302,304,305,309,310,312,321,322,359,360,361,362,366,375,376,377,378,379,394", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13781,13849,13926,13988,14056,14133,14191,14489,14542,14618,14695,14919,15096,15195,15348,15697,15984,17001,17245,17338,17407,17563,17650,17738,17907,18210,18300,18376,18473,18552,18648,18736,18825,18910,19011,19160,19409,19667,19739,19804,21248,21900,21974,22089,22184,22239,22332,22417,22484,22572,22660,22717,23286,23335,23412,23518,23590,24008,24382,24461,24553,24600,24890,24961,25019,25079,25259,25421,25735,25802,26643,26721,26894,26974,27311,27384,27538,28231,28315,32012,32065,32197,32245,32665,33986,34052,34121,34185,34255,35579", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,45,78,91,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,83,84,52,131,47,53,68,65,68,63,69,87,79", "endOffsets": "13844,13921,13983,14051,14128,14186,14245,14537,14613,14690,14771,15013,15190,15276,15423,15770,16062,17093,17333,17402,17492,17645,17733,17842,17983,18295,18371,18468,18547,18643,18731,18820,18905,19006,19081,19228,19508,19734,19799,20476,21895,21969,22084,22179,22234,22327,22412,22479,22567,22655,22712,22790,23330,23407,23513,23585,23660,24049,24456,24548,24595,24643,24956,25014,25074,25254,25416,25540,25797,25881,26716,26813,26969,27051,27379,27468,27610,28310,28395,32060,32192,32240,32294,32729,34047,34116,34180,34250,34338,35654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,205,316,375,462,516,574,650,756,919,993,1062,1146,1237,1358,1487,1560,1634,1797,1866,1925,1999,2080,2221,2350,2415,2529,2615,2690,2775,2843", "endColumns": "70,78,110,58,86,53,57,75,105,162,73,68,83,90,120,128,72,73,162,68,58,73,80,140,128,64,113,85,74,84,67,155", "endOffsets": "121,200,311,370,457,511,569,645,751,914,988,1057,1141,1232,1353,1482,1555,1629,1792,1861,1920,1994,2075,2216,2345,2410,2524,2610,2685,2770,2838,2994"}, "to": {"startLines": "164,166,300,316,358,369,370,371,372,373,386,387,388,389,390,391,392,393,395,396,397,398,399,400,401,402,403,404,405,406,407,408", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14250,14410,26532,27868,31925,33459,33513,33571,33647,33753,34864,34938,35007,35091,35182,35303,35432,35505,35659,35822,35891,35950,36024,36105,36246,36375,36440,36554,36640,36715,36800,36868", "endColumns": "70,78,110,58,86,53,57,75,105,162,73,68,83,90,120,128,72,73,162,68,58,73,80,140,128,64,113,85,74,84,67,155", "endOffsets": "14316,14484,26638,27922,32007,33508,33566,33642,33748,33911,34933,35002,35086,35177,35298,35427,35500,35574,35817,35886,35945,36019,36100,36241,36370,36435,36549,36635,36710,36795,36863,37019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,238,359,458,547,668,845,1033,1173,1264,1367,1460,1561,1655,1714,1778,1875,1986,2210,2287,2376,2447,2523,2636,2722,2812,2879,2938,3040,3145,3273,3364,3435,3505,3592,3664,3770,3884", "endColumns": "88,93,120,98,88,120,176,187,139,90,102,92,100,93,58,63,96,110,223,76,88,70,75,112,85,89,66,58,101,104,127,90,70,69,86,71,105,113,86", "endOffsets": "139,233,354,453,542,663,840,1028,1168,1259,1362,1455,1556,1650,1709,1773,1870,1981,2205,2282,2371,2442,2518,2631,2717,2807,2874,2933,3035,3140,3268,3359,3430,3500,3587,3659,3765,3879,3966"}, "to": {"startLines": "165,243,260,270,271,318,324,325,326,327,329,330,331,333,334,335,336,337,338,339,340,341,342,343,348,349,350,351,352,353,354,355,356,374,381,382,383,384,385", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14321,21154,23101,24054,24153,27988,28460,28637,28825,28965,29156,29259,29352,29534,29628,29687,29751,29848,29959,30183,30260,30349,30420,30496,30949,31035,31125,31192,31251,31353,31458,31586,31677,33916,34398,34485,34557,34663,34777", "endColumns": "88,93,120,98,88,120,176,187,139,90,102,92,100,93,58,63,96,110,223,76,88,70,75,112,85,89,66,58,101,104,127,90,70,69,86,71,105,113,86", "endOffsets": "14405,21243,23217,24148,24237,28104,28632,28820,28960,29051,29254,29347,29448,29623,29682,29746,29843,29954,30178,30255,30344,30415,30491,30604,31030,31120,31187,31246,31348,31453,31581,31672,31743,33981,34480,34552,34658,34772,34859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be80f888898887bb71616e48bf7874ba\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "48,49,71,72,74,93,94,145,146,148,149,152,153,155,410,411,412", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4467,7017,7114,7290,8826,8902,12771,12860,13020,13084,13368,13448,13610,37193,37270,37337", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "4462,4543,7109,7209,7373,8897,8985,12855,12937,13079,13143,13443,13525,13675,37265,37332,37452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,209,257,324,421,490,719,784,882,948,1003,1067,1362,1436,1502,1555,1608,1700,1826,1933,1990,2074,2150,2233,2298,2397,2474,2551,2612,2672,2734,2794,2894,2975,3078,3151,3230,3315,3492,3684,3802,3858,4518,4583", "endColumns": "153,47,66,96,68,228,64,97,65,54,63,294,73,65,52,52,91,125,106,56,83,75,82,64,98,76,76,60,59,61,59,99,80,102,72,78,84,176,191,117,55,659,64,54", "endOffsets": "204,252,319,416,485,714,779,877,943,998,1062,1357,1431,1497,1550,1603,1695,1821,1928,1985,2069,2145,2228,2293,2392,2469,2546,2607,2667,2729,2789,2889,2970,3073,3146,3225,3310,3487,3679,3797,3853,4513,4578,4633"}, "to": {"startLines": "232,236,237,238,239,240,241,242,256,259,261,267,272,273,280,290,293,294,295,296,297,303,306,311,313,314,315,317,319,320,323,328,332,344,345,346,347,357,363,364,365,367,368,380", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19513,20481,20529,20596,20693,20762,20991,21056,22795,23046,23222,23665,24242,24316,24773,25682,25886,25978,26104,26211,26268,26818,27056,27473,27615,27714,27791,27927,28109,28169,28400,29056,29453,30609,30712,30785,30864,31748,32299,32491,32609,32734,33394,34343", "endColumns": "153,47,66,96,68,228,64,97,65,54,63,294,73,65,52,52,91,125,106,56,83,75,82,64,98,76,76,60,59,61,59,99,80,102,72,78,84,176,191,117,55,659,64,54", "endOffsets": "19662,20524,20591,20688,20757,20986,21051,21149,22856,23096,23281,23955,24311,24377,24821,25730,25973,26099,26206,26263,26347,26889,27134,27533,27709,27786,27863,27983,28164,28226,28455,29151,29529,30707,30780,30859,30944,31920,32486,32604,32660,33389,33454,34393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "70,88,89,90", "startColumns": "4,4,4,4", "startOffsets": "6907,8387,8488,8600", "endColumns": "109,100,111,96", "endOffsets": "7012,8483,8595,8692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4623,4729,4888,5014,5123,5279,5409,5529,5762,5916,6023,6184,6312,6454,6630,6697,6759", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4724,4883,5009,5118,5274,5404,5524,5627,5911,6018,6179,6307,6449,6625,6692,6754,6832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e71e7c08b297cb32539b89f832f53d8\\transformed\\jetified-material3-1.0.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,72", "endOffsets": "125,201,274"}, "to": {"startLines": "50,73,86", "startColumns": "4,4,4", "startOffsets": "4548,7214,8222", "endColumns": "74,75,72", "endOffsets": "4618,7285,8290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,13530", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,13605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5632", "endColumns": "129", "endOffsets": "5757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,399,469,528,604,676,742,802", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "128,185,247,332,394,464,523,599,671,737,797,866"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14841,15640,15775,15837,15922,16203,16733,16925,17173,17497,17847,18141", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "14914,15692,15832,15917,15979,16268,16787,16996,17240,17558,17902,18205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7442,7518,7580,7644,7715,7795,7873,7967,8064", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "7513,7575,7639,7710,7790,7868,7962,8059,8130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba2bbf823ab5a18be680588e75f7e6ba\\transformed\\jetified-play-services-wallet-19.2.1\\res\\values-nb\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "91,415", "startColumns": "4,4", "startOffsets": "8697,37612", "endColumns": "60,74", "endOffsets": "8753,37682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,349,416,477,546,613,677,745,817,877,936,1009,1073,1143,1206,1281,1345,1434,1508,1593,1684,1782,1869,1917,1965,2042,2106,2173,2243,2337,2423,2511", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "115,193,260,344,411,472,541,608,672,740,812,872,931,1004,1068,1138,1201,1276,1340,1429,1503,1588,1679,1777,1864,1912,1960,2037,2101,2168,2238,2332,2418,2506,2590"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,257,258,268,278,279,281,288,289,298,299,307,308", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14776,15018,15281,15428,15512,15579,16067,16136,16273,16337,16405,16477,16537,16596,16669,16792,16862,17098,17988,18052,19086,19233,19318,22861,22959,23960,24648,24696,24826,25545,25612,26352,26446,27139,27227", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "14836,15091,15343,15507,15574,15635,16131,16198,16332,16400,16472,16532,16591,16664,16728,16857,16920,17168,18047,18136,19155,19313,19404,22954,23041,24003,24691,24768,24885,25607,25677,26441,26527,27222,27306"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "69,85,147,151,409,413,414", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6837,8135,12942,13226,37024,37457,37536", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "6902,8217,13015,13363,37188,37531,37607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1030,1122,1190,1250,1337,1401,1463,1527,1595,1660,1714,1823,1881,1943,1997,2072,2192,2274,2354,2488,2566,2646,2734,2788,2839,2905,2973,3047,3137,3208,3286,3356,3426,3515,3593,3681,3771,3843,3915,3999,4050,4116,4197,4280,4342,4406,4469,4569,4667,4760,4858,4916,4971", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,87,53,50,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,57,54,77", "endOffsets": "256,333,406,493,581,661,760,879,961,1025,1117,1185,1245,1332,1396,1458,1522,1590,1655,1709,1818,1876,1938,1992,2067,2187,2269,2349,2483,2561,2641,2729,2783,2834,2900,2968,3042,3132,3203,3281,3351,3421,3510,3588,3676,3766,3838,3910,3994,4045,4111,4192,4275,4337,4401,4464,4564,4662,4755,4853,4911,4966,5044"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,7378,8295,8758,8990,9050,9137,9201,9263,9327,9395,9460,9514,9623,9681,9743,9797,9872,9992,10074,10154,10288,10366,10446,10534,10588,10639,10705,10773,10847,10937,11008,11086,11156,11226,11315,11393,11481,11571,11643,11715,11799,11850,11916,11997,12080,12142,12206,12269,12369,12467,12560,12658,12716,13148", "endLines": "5,33,34,35,36,37,45,46,47,75,87,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,87,53,50,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,57,54,77", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,7437,8382,8821,9045,9132,9196,9258,9322,9390,9455,9509,9618,9676,9738,9792,9867,9987,10069,10149,10283,10361,10441,10529,10583,10634,10700,10768,10842,10932,11003,11081,11151,11221,11310,11388,11476,11566,11638,11710,11794,11845,11911,11992,12075,12137,12201,12264,12364,12462,12555,12653,12711,12766,13221"}}]}]}