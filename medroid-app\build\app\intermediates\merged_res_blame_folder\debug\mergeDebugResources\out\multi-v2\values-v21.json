{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,36,38,39,40,41,43,45,46,47,48,49,51,53,55,57,59,61,62,67,69,71,72,73,75,77,78,79,80,85,97,140,143,186,201,213,215,217,219,222,226,229,230,231,234,235,236,237,238,239,242,243,245,247,249,251,255,257,258,259,260,262,266,268,270,271,272,273,274,275,306,307,308,318,319,320,332", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1668,1759,1862,1965,2070,2177,2286,2395,2504,2613,2722,2829,2932,3051,3206,3361,3466,3587,3688,3835,3976,4079,4198,4305,4408,4563,4734,4883,5048,5205,5356,5475,5826,5975,6124,6236,6383,6536,6683,6758,6847,6934,7459,8551,11309,11494,14264,15397,16249,16372,16495,16608,16791,17046,17247,17336,17447,17680,17781,17876,17999,18128,18245,18422,18521,18656,18799,18934,19053,19254,19373,19466,19577,19633,19740,19935,20046,20179,20274,20365,20456,20549,20666,23099,23170,23253,23876,23933,23991,24615", "endLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,35,37,38,39,40,42,44,45,46,47,48,50,52,54,56,58,60,61,66,68,70,71,72,74,76,77,78,79,80,85,139,142,185,188,203,214,216,218,221,225,228,229,230,233,234,235,236,237,238,241,242,244,246,248,250,254,256,257,258,259,261,265,267,269,270,271,272,273,274,276,306,307,317,318,319,331,343", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1754,1857,1960,2065,2172,2281,2390,2499,2608,2717,2824,2927,3046,3201,3356,3461,3582,3683,3830,3971,4074,4193,4300,4403,4558,4729,4878,5043,5200,5351,5470,5821,5970,6119,6231,6378,6531,6678,6753,6842,6929,7030,7557,11304,11489,14259,14456,15591,16367,16490,16603,16786,17041,17242,17331,17442,17675,17776,17871,17994,18123,18240,18417,18516,18651,18794,18929,19048,19249,19368,19461,19572,19628,19735,19930,20041,20174,20269,20360,20451,20544,20661,20800,23165,23248,23871,23928,23986,24610,25246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46567eeb5dd96eb1eece9970999eba2d\\transformed\\media-1.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "352,355,359,363", "startColumns": "4,4,4,4", "startOffsets": "25827,25995,26284,26580", "endLines": "354,357,361,365", "endColumns": "12,12,12,12", "endOffsets": "25990,26153,26447,26742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,350,351,358,362,567,570", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,25585,25701,26158,26452,38615,38787", "endLines": "2,17,18,19,350,351,358,362,569,574", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,25696,25822,26279,26575,38782,39134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,6", "startColumns": "4,4,4", "startOffsets": "55,120,276", "endLines": "2,5,8", "endColumns": "64,12,12", "endOffsets": "115,271,449"}, "to": {"startLines": "20,344,347", "startColumns": "4,4,4", "startOffsets": "1603,25251,25407", "endLines": "20,346,349", "endColumns": "64,12,12", "endOffsets": "1663,25402,25580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,25,28,31,34,37,40,43,46,49,52,55,56,59,64,75,81,91,101,111,121,131,141,151,161,171,181,191,201,211,221,231,237,243,249,255,259,263,264,265,266,270,273,276,279,282,283,286,289,293,297", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,1960,2173,2432,2703,2921,3153,3389,3639,3852,4061,4292,4493,4609,4779,5100,6129,6586,7137,7692,8248,8809,9361,9912,10464,11017,11566,12119,12675,13230,13776,14330,14885,15177,15471,15771,16071,16400,16741,16879,17023,17179,17572,17790,18012,18238,18454,18564,18734,18924,19165,19424", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,51,54,55,58,63,74,80,90,100,110,120,130,140,150,160,170,180,190,200,210,220,230,236,242,248,254,258,262,263,264,265,269,272,275,278,281,282,285,288,292,296,299", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,1955,2168,2427,2698,2916,3148,3384,3634,3847,4056,4287,4488,4604,4774,5095,6124,6581,7132,7687,8243,8804,9356,9907,10459,11012,11561,12114,12670,13225,13771,14325,14880,15172,15466,15766,16066,16395,16736,16874,17018,17174,17567,17785,18007,18233,18449,18559,18729,18919,19160,19419,19596"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,81,82,83,84,86,87,88,91,94,189,192,195,198,204,207,210,277,280,281,284,289,300,366,376,386,396,406,416,426,436,446,456,466,476,486,496,506,516,522,528,534,540,544,548,549,550,551,555,558,561,564,575,576,579,582,586,590", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,7035,7128,7235,7340,7562,7687,7808,8021,8280,14461,14679,14911,15147,15596,15809,16018,20805,21006,21122,21292,21613,22642,26747,27298,27853,28409,28970,29522,30073,30625,31178,31727,32280,32836,33391,33937,34491,35046,35338,35632,35932,36232,36561,36902,37040,37184,37340,37733,37951,38173,38399,39139,39249,39419,39609,39850,40109", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,81,82,83,84,86,87,90,93,96,191,194,197,200,206,209,212,279,280,283,288,299,305,375,385,395,405,415,425,435,445,455,465,475,485,495,505,515,521,527,533,539,543,547,548,549,550,554,557,560,563,566,575,578,581,585,589,592", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,7123,7230,7335,7454,7682,7803,8016,8275,8546,14674,14906,15142,15392,15804,16013,16244,21001,21117,21287,21608,22637,23094,27293,27848,28404,28965,29517,30068,30620,31173,31722,32275,32831,33386,33932,34486,35041,35333,35627,35927,36227,36556,36897,37035,37179,37335,37728,37946,38168,38394,38610,39244,39414,39604,39845,40104,40281"}}]}]}