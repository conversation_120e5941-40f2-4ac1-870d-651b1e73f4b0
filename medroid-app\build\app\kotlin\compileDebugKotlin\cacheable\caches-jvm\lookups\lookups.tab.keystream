  
FlutterEngine android.app.Activity  GeneratedPluginRegistrant android.app.Activity  configureFlutterEngine android.app.Activity  
FlutterEngine android.content.Context  GeneratedPluginRegistrant android.content.Context  configureFlutterEngine android.content.Context  
FlutterEngine android.content.ContextWrapper  GeneratedPluginRegistrant android.content.ContextWrapper  configureFlutterEngine android.content.ContextWrapper  
FlutterEngine  android.view.ContextThemeWrapper  GeneratedPluginRegistrant  android.view.ContextThemeWrapper  configureFlutterEngine  android.view.ContextThemeWrapper  
FlutterEngine #androidx.activity.ComponentActivity  GeneratedPluginRegistrant #androidx.activity.ComponentActivity  configureFlutterEngine #androidx.activity.ComponentActivity  GeneratedPluginRegistrant -androidx.activity.ComponentActivity.Companion  
FlutterEngine #androidx.core.app.ComponentActivity  GeneratedPluginRegistrant #androidx.core.app.ComponentActivity  configureFlutterEngine #androidx.core.app.ComponentActivity  
FlutterEngine &androidx.fragment.app.FragmentActivity  GeneratedPluginRegistrant &androidx.fragment.app.FragmentActivity  configureFlutterEngine &androidx.fragment.app.FragmentActivity  GeneratedPluginRegistrant com.example.medroid_app  MainActivity com.example.medroid_app  
FlutterEngine $com.example.medroid_app.MainActivity  GeneratedPluginRegistrant $com.example.medroid_app.MainActivity  FlutterActivity io.flutter.embedding.android  FlutterFragmentActivity io.flutter.embedding.android  
FlutterEngine 4io.flutter.embedding.android.FlutterFragmentActivity  GeneratedPluginRegistrant 4io.flutter.embedding.android.FlutterFragmentActivity  configureFlutterEngine 4io.flutter.embedding.android.FlutterFragmentActivity  
FlutterEngine io.flutter.embedding.engine  GeneratedPluginRegistrant io.flutter.plugins  registerWith ,io.flutter.plugins.GeneratedPluginRegistrant  GeneratedPluginRegistrant 	java.lang  GeneratedPluginRegistrant kotlin  GeneratedPluginRegistrant kotlin.annotation  GeneratedPluginRegistrant kotlin.collections  GeneratedPluginRegistrant kotlin.comparisons  GeneratedPluginRegistrant 	kotlin.io  GeneratedPluginRegistrant 
kotlin.jvm  GeneratedPluginRegistrant 
kotlin.ranges  GeneratedPluginRegistrant kotlin.sequences  GeneratedPluginRegistrant kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                