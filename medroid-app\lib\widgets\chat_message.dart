import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:medroid_app/models/chat_message.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/theme.dart';

class ChatMessageWidget extends StatefulWidget {
  final ChatMessage message;
  final VoidCallback? onLoadMore;
  final bool isTyping;
  final Function(Map<String, dynamic>, Map<String, dynamic>)? onBookAppointment;

  const ChatMessageWidget({
    Key? key,
    required this.message,
    this.onLoadMore,
    this.isTyping = false,
    this.onBookAppointment,
  }) : super(key: key);

  @override
  State<ChatMessageWidget> createState() => _ChatMessageWidgetState();
}

class _ChatMessageWidgetState extends State<ChatMessageWidget> {
  bool _showTimestamp = false;

  // Helper method to convert text to sentence case
  String toSentenceCase(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  // Helper method to handle URL taps in markdown
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch $url')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isUser = widget.message.isUser;
    final brightness = Theme.of(context).brightness;
    final isLightMode = brightness == Brightness.light;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isUser) _buildUserAvatar(context),
          const SizedBox(width: 8),
          Flexible(
            child: isUser
                ? Align(
                    alignment: Alignment.centerLeft,
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.7,
                      ),
                      child: IntrinsicWidth(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _showTimestamp = !_showTimestamp;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: isLightMode
                                  ? Colors.grey.shade200
                                  : Colors.grey.shade800,
                              borderRadius: BorderRadius.circular(
                                  8), // More square with light curves
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (widget.isTyping)
                                  // Show typing indicator for user
                                  Row(
                                    children: [
                                      Container(
                                        width: 8,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryColor,
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Container(
                                        width: 8,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryColor
                                              .withAlpha(179), // 0.7 opacity
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Container(
                                        width: 8,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryColor
                                              .withAlpha(102), // 0.4 opacity
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                      ),
                                    ],
                                  )
                                else if (widget.message.isText)
                                  Text(
                                    toSentenceCase(widget.message.content),
                                    style: GoogleFonts.inter(
                                      height: 1.6,
                                      fontSize: 15,
                                    ),
                                  )
                                else if (widget.message.isImage)
                                  _buildImageContent(context, isUser),
                                if (_showTimestamp) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    widget.message.formattedTime,
                                    style: GoogleFonts.inter(
                                      fontSize: 10,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      setState(() {
                        _showTimestamp = !_showTimestamp;
                      });
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widget.isTyping)
                          // Show typing indicator
                          Padding(
                            padding: const EdgeInsets.only(right: 48),
                            child: Row(
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor
                                        .withAlpha(179), // 0.7 opacity
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor
                                        .withAlpha(102), // 0.4 opacity
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ],
                            ),
                          )
                        else if (widget.message.isAppointment)
                          _buildAppointmentContent(context)
                        else if (widget.message.isAppointmentSlots)
                          _buildAppointmentSlotsContent(context)
                        else if (widget.message.isText)
                          Container(
                            constraints: BoxConstraints(
                              maxWidth: MediaQuery.of(context).size.width * 0.8,
                            ),
                            padding: const EdgeInsets.only(right: 16),
                            child: Text(
                              widget.message.content,
                              style: GoogleFonts.inter(
                                height: 1.6,
                                fontSize: 15,
                                color:
                                    isLightMode ? Colors.black87 : Colors.white,
                              ),
                            ),
                          )
                        else if (widget.message.isImage)
                          _buildImageContent(context, isUser),
                        if (_showTimestamp) ...[
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                widget.message.formattedTime,
                                style: GoogleFonts.inter(
                                  fontSize: 10,
                                  color: Colors.grey,
                                ),
                              ),
                              // Show "Load More" button for truncated AI responses
                              if (widget.message.isTruncated &&
                                  widget.onLoadMore != null)
                                TextButton(
                                  onPressed: widget.onLoadMore,
                                  style: TextButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 0),
                                    minimumSize: const Size(0, 0),
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                                  child: Text(
                                    'Load More',
                                    style: GoogleFonts.inter(
                                      fontSize: 10,
                                      color: AppTheme.primaryColor,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ] else if (widget.message.isTruncated &&
                            widget.onLoadMore != null) ...[
                          const SizedBox(height: 4),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: widget.onLoadMore,
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 0),
                                minimumSize: const Size(0, 0),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: Text(
                                'Load More',
                                style: GoogleFonts.inter(
                                  fontSize: 10,
                                  color: AppTheme.primaryColor,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserAvatar(BuildContext context) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withAlpha(50),
        shape: BoxShape.circle,
      ),
      child: const Center(
        child: Icon(
          Icons.person,
          size: 16,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildImageContent(BuildContext context, bool isUser) {
    // If we have a local image path, display it from the file system
    if (widget.message.localImagePath != null &&
        widget.message.localImagePath!.isNotEmpty) {
      // Mobile-only: Use Image.file for local images
      return ClipRRect(
        borderRadius: BorderRadius.circular(4), // More square with light curves
        child: Image.file(
          File(widget.message.localImagePath!),
          width: 200,
          height: 200,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: 200,
              height: 200,
              color: Colors.grey.shade200,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.image, color: Colors.grey),
                    const SizedBox(height: 8),
                    Text(
                      'Image Preview',
                      style: GoogleFonts.inter(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    }
    // If we have an image URL, display it from the network
    else if (widget.message.imageUrl != null &&
        widget.message.imageUrl!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(4), // More square with light curves
        child: CachedNetworkImage(
          imageUrl: widget.message.imageUrl!,
          fit: BoxFit.cover,
          width: 200,
          height: 200,
          placeholder: (context, url) => Container(
            width: 200,
            height: 200,
            color: Colors.grey.shade200,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            width: 200,
            height: 200,
            color: Colors.grey.shade300,
            child: const Center(
              child: Icon(
                Icons.error,
                color: Colors.red,
              ),
            ),
          ),
        ),
      );
    }
    // If we don't have an image, display a placeholder
    else {
      return Container(
        width: 200,
        height: 200,
        color: Colors.grey.shade300,
        child: const Center(
          child: Icon(
            Icons.image_not_supported,
            color: Colors.grey,
          ),
        ),
      );
    }
  }

  Widget _buildAppointmentContent(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.85,
      ),
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.tealSurge, // Teal Surge
            AppColors.tealSurge.withAlpha(204), // 80% opacity darker teal
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.tealSurge.withAlpha(51), // 20% opacity
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(51), // 20% opacity
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'APPOINTMENT CONFIRMED',
                    style: GoogleFonts.inter(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Content with text formatting
            Text(
              widget.message.content,
              style: GoogleFonts.inter(
                height: 1.5,
                fontSize: 15,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 16),

            // Footer with celebration emoji
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(26), // 10% opacity
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '🎉 Your appointment is secured! 🎉',
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentSlotsContent(BuildContext context) {
    final slots = widget.message.appointmentSlots ?? [];

    if (slots.isEmpty) {
      return Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.85,
        ),
        margin: const EdgeInsets.only(right: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Text(
          'No appointment slots available at the moment.',
          style: GoogleFonts.inter(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.85,
      ),
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(51), // 20% opacity
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.tealSurge.withAlpha(26), // 10% opacity
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: AppColors.tealSurge,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Available Appointment Slots',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.tealSurge,
                  ),
                ),
              ],
            ),
          ),

          // Slots grid
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${slots.length} slots available • Tap any slot to book',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 12),

                // Display slots in a grid
                ...slots
                    .take(6)
                    .map((slotGroup) => _buildSlotGroup(context, slotGroup))
                    .toList(),

                if (slots.length > 6) ...[
                  const SizedBox(height: 8),
                  Center(
                    child: Text(
                      '+ ${slots.length - 6} more slots available',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppColors.tealSurge,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSlotGroup(BuildContext context, dynamic slotGroup) {
    final provider = slotGroup['provider'] ?? {};
    final providerName = provider['name'] ?? 'Unknown Provider';
    final date = slotGroup['date'] ?? '';
    final dayOfWeek = slotGroup['day_of_week'] ?? '';
    final slots = slotGroup['slots'] ?? [];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Provider and date info
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      providerName,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      '$dayOfWeek, $date',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          if (slots.isNotEmpty) ...[
            const SizedBox(height: 8),
            // Time slots
            Wrap(
              spacing: 8,
              runSpacing: 6,
              children: slots.take(4).map<Widget>((slot) {
                final startTime = slot['start_time'] ?? '';
                return GestureDetector(
                  onTap: () => _onSlotTapped(context, slotGroup, slot),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.tealSurge,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      startTime,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),

            if (slots.length > 4)
              Padding(
                padding: const EdgeInsets.only(top: 6),
                child: Text(
                  '+ ${slots.length - 4} more times',
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  void _onSlotTapped(BuildContext context, dynamic slotGroup, dynamic slot) {
    // Show a modal with all available slots for this provider/date
    // This will open the full appointment slot selector
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Appointment Time',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${slotGroup['provider']['name']} • ${slotGroup['day_of_week']}, ${slotGroup['date']}',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: (slotGroup['slots'] as List).length,
                  itemBuilder: (context, index) {
                    final timeSlot = (slotGroup['slots'] as List)[index];
                    final startTime = timeSlot['start_time'] ?? '';
                    final endTime = timeSlot['end_time'] ?? '';

                    return GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _bookAppointment(context, slotGroup, timeSlot);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              AppColors.tealSurge.withAlpha(26), // 10% opacity
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color: AppColors.tealSurge
                                  .withAlpha(77)), // 30% opacity
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                startTime,
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.tealSurge,
                                ),
                              ),
                              Text(
                                endTime,
                                style: GoogleFonts.inter(
                                  fontSize: 11,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _bookAppointment(
      BuildContext context, dynamic slotGroup, dynamic timeSlot) {
    // Call the parent widget's booking method if available
    if (widget.onBookAppointment != null) {
      widget.onBookAppointment!(slotGroup, timeSlot);
    } else {
      // Fallback: show a snackbar if no callback is provided
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Booking appointment with ${slotGroup['provider']['name']} at ${timeSlot['start_time']}',
          ),
          backgroundColor: AppColors.tealSurge,
        ),
      );
    }
  }
}
