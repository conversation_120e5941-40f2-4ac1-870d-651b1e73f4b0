#!/bin/bash

# Test the web-api/chat/start endpoint after fix

echo "🧪 TESTING web-api/chat/start ENDPOINT"
echo "======================================"
echo ""

# Test the endpoint
echo "Testing POST https://app.medroid.ai/web-api/chat/start"
echo "-----------------------------------------------------"

# Test without authentication (should return 302 redirect to login or 401)
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" \
    -X POST https://app.medroid.ai/web-api/chat/start \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{}')

echo "HTTP Status Code: $HTTP_CODE"

case $HTTP_CODE in
    200)
        echo "✅ SUCCESS: Route is working!"
        ;;
    302)
        echo "🔄 REDIRECT: Route exists but requires authentication (this is expected)"
        echo "   The route is working correctly - user needs to login first"
        ;;
    401)
        echo "🔐 UNAUTHORIZED: Route exists but requires authentication (this is expected)"
        echo "   The route is working correctly - user needs to login first"
        ;;
    404)
        echo "❌ NOT FOUND: Route still not found - may need additional fixes"
        ;;
    419)
        echo "🛡️  CSRF: Route exists but CSRF token required"
        echo "   The route is working correctly - need CSRF token for web requests"
        ;;
    422)
        echo "📝 VALIDATION: Route exists but validation failed"
        echo "   The route is working correctly - check request data"
        ;;
    500)
        echo "💥 SERVER ERROR: Route exists but server error occurred"
        echo "   Check Laravel logs: tail -f storage/logs/laravel.log"
        ;;
    000)
        echo "🌐 CONNECTION: Could not connect to server"
        ;;
    *)
        echo "❓ UNKNOWN: Unexpected response code"
        ;;
esac

echo ""
echo "📋 SUMMARY:"
echo "==========="

if [ "$HTTP_CODE" = "404" ]; then
    echo "❌ Route is still not found. Additional debugging needed."
    echo ""
    echo "Next steps:"
    echo "1. Check if route cache was properly cleared"
    echo "2. Verify route definition in routes/web.php"
    echo "3. Check Laravel logs for errors"
    echo "4. Restart web server manually if possible"
else
    echo "✅ Route is now accessible!"
    echo ""
    echo "The route requires authentication. To use it:"
    echo "1. User must login to the web application first"
    echo "2. Include session cookies in the request"
    echo "3. Include CSRF token for web requests"
    echo ""
    echo "For API access (token-based), use routes in api.php instead"
fi

echo ""
echo "🔍 Additional debugging commands:"
echo "================================"
echo "Check route list: php artisan route:list | grep chat"
echo "Check Laravel logs: tail -f storage/logs/laravel.log"
echo "Test with authentication: Login to web app first, then test"
