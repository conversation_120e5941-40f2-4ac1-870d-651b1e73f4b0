#!/bin/bash

# Production Route Fix Script for Medroid Application
# This script fixes the missing route issue on the live server

echo "=== MEDROID PRODUCTION ROUTE FIX ==="
echo "Starting fix process at $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

# Function to run commands with error checking
run_command() {
    echo "Running: $1"
    if eval "$1"; then
        print_status "Command completed successfully"
    else
        print_error "Command failed: $1"
        return 1
    fi
    echo ""
}

# Check if we're in the correct directory
if [ ! -f "artisan" ]; then
    print_error "artisan file not found. Please run this script from the Laravel root directory."
    exit 1
fi

print_status "Found Laravel installation"

# Step 1: Backup current state
echo "1. CREATING BACKUP"
echo "=================="

BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup important cache files
if [ -d "bootstrap/cache" ]; then
    cp -r bootstrap/cache "$BACKUP_DIR/"
    print_status "Backed up bootstrap/cache to $BACKUP_DIR"
fi

if [ -d "storage/framework/cache" ]; then
    cp -r storage/framework/cache "$BACKUP_DIR/"
    print_status "Backed up storage cache to $BACKUP_DIR"
fi

echo ""

# Step 2: Clear all caches
echo "2. CLEARING CACHES"
echo "=================="

run_command "php artisan cache:clear"
run_command "php artisan config:clear"
run_command "php artisan route:clear"
run_command "php artisan view:clear"
run_command "php artisan optimize:clear"

# Remove specific cache files
if [ -f "bootstrap/cache/routes-v7.php" ]; then
    rm -f bootstrap/cache/routes-v7.php
    print_status "Removed routes cache file"
fi

if [ -f "bootstrap/cache/compiled.php" ]; then
    rm -f bootstrap/cache/compiled.php
    print_status "Removed compiled.php"
fi

if [ -f "bootstrap/cache/services.php" ]; then
    rm -f bootstrap/cache/services.php
    print_status "Removed services.php"
fi

echo ""

# Step 3: Check route file syntax
echo "3. VALIDATING ROUTE FILES"
echo "========================="

if php -l routes/web.php > /dev/null 2>&1; then
    print_status "routes/web.php syntax is valid"
else
    print_error "routes/web.php has syntax errors"
    php -l routes/web.php
fi

if php -l routes/api.php > /dev/null 2>&1; then
    print_status "routes/api.php syntax is valid"
else
    print_error "routes/api.php has syntax errors"
    php -l routes/api.php
fi

echo ""

# Step 4: Check if route exists in file
echo "4. CHECKING ROUTE DEFINITION"
echo "============================"

if grep -q "web-api/chat" routes/web.php; then
    print_status "Found web-api/chat routes in web.php"
    
    if grep -q "Route::post('start'" routes/web.php; then
        print_status "Found start route definition"
    else
        print_warning "Start route definition not found"
    fi
else
    print_error "web-api/chat routes not found in web.php"
fi

echo ""

# Step 5: Test route registration
echo "5. TESTING ROUTE REGISTRATION"
echo "============================="

run_command "php artisan route:list --compact | head -5"

# Check if specific route exists
if php artisan route:list | grep -q "web-api/chat/start"; then
    print_status "Route web-api/chat/start is registered"
else
    print_warning "Route web-api/chat/start not found in route list"
fi

echo ""

# Step 6: Re-optimize for production
echo "6. RE-OPTIMIZING FOR PRODUCTION"
echo "==============================="

run_command "php artisan config:cache"
run_command "php artisan route:cache"
run_command "php artisan view:cache"

# Only run optimize if not in debug mode
if ! grep -q "APP_DEBUG=true" .env; then
    run_command "php artisan optimize"
fi

echo ""

# Step 7: Set proper permissions
echo "7. SETTING PERMISSIONS"
echo "======================"

# Set proper permissions for Laravel
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache 2>/dev/null || true

print_status "Permissions set for storage and bootstrap/cache"

echo ""

# Step 8: Restart services (if possible)
echo "8. RESTARTING SERVICES"
echo "======================"

# Try to restart PHP-FPM (common service names)
for service in php8.1-fpm php8.0-fpm php7.4-fpm php-fpm; do
    if systemctl is-active --quiet $service 2>/dev/null; then
        if systemctl restart $service 2>/dev/null; then
            print_status "Restarted $service"
            break
        fi
    fi
done

# Try to restart web server
for service in nginx apache2 httpd; do
    if systemctl is-active --quiet $service 2>/dev/null; then
        if systemctl reload $service 2>/dev/null; then
            print_status "Reloaded $service"
            break
        fi
    fi
done

echo ""

# Step 9: Final verification
echo "9. FINAL VERIFICATION"
echo "===================="

echo "Checking route registration..."
if php artisan route:list | grep -q "POST.*web-api/chat/start"; then
    print_status "Route is properly registered!"
else
    print_warning "Route may not be registered correctly"
fi

echo ""
echo "Checking controller..."
if [ -f "app/Http/Controllers/AIChatController.php" ]; then
    if grep -q "startConversation" app/Http/Controllers/AIChatController.php; then
        print_status "Controller method exists"
    else
        print_warning "Controller method may be missing"
    fi
else
    print_error "AIChatController.php not found"
fi

echo ""

# Step 10: Test endpoint (if curl is available)
echo "10. TESTING ENDPOINT"
echo "==================="

if command -v curl > /dev/null; then
    echo "Testing endpoint with curl..."
    
    # Get the app URL from .env or use localhost
    APP_URL=$(grep "APP_URL=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "http://localhost")
    
    # Test the endpoint
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$APP_URL/web-api/chat/start" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d '{}' 2>/dev/null || echo "000")
    
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "422" ] || [ "$HTTP_CODE" = "401" ]; then
        print_status "Endpoint is responding (HTTP $HTTP_CODE)"
    elif [ "$HTTP_CODE" = "404" ]; then
        print_error "Endpoint still returns 404"
    else
        print_warning "Endpoint returned HTTP $HTTP_CODE"
    fi
else
    print_warning "curl not available for endpoint testing"
fi

echo ""

# Summary
echo "=== FIX SUMMARY ==="
echo "Backup created in: $BACKUP_DIR"
echo "All caches cleared and regenerated"
echo "Services restarted (if possible)"
echo ""

echo "If the issue persists:"
echo "1. Check Laravel logs: tail -f storage/logs/laravel.log"
echo "2. Check web server error logs"
echo "3. Verify .env configuration"
echo "4. Check file permissions"
echo ""

echo "Fix completed at $(date)"
echo "===================="
