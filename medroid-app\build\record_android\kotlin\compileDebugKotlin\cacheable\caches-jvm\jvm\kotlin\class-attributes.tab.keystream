!com.llfbandit.record.RecordPlugin+com.llfbandit.record.RecordPlugin.Companion5com.llfbandit.record.methodcall.MethodCallHandlerImpl/com.llfbandit.record.methodcall.RecorderWrapper9com.llfbandit.record.methodcall.RecorderWrapper.Companion1com.llfbandit.record.permission.PermissionManager;com.llfbandit.record.permission.PermissionManager.Companion8com.llfbandit.record.permission.PermissionResultCallback%com.llfbandit.record.record.PCMReader/com.llfbandit.record.record.PCMReader.Companion(com.llfbandit.record.record.RecordConfig(com.llfbandit.record.record.AudioEncoder2com.llfbandit.record.record.AudioEncoder.Companion'com.llfbandit.record.record.RecordState:com.llfbandit.record.record.bluetooth.BluetoothScoListener7com.llfbandit.record.record.bluetooth.BluetoothReceiver3com.llfbandit.record.record.container.AdtsContainer3com.llfbandit.record.record.container.FlacContainer=com.llfbandit.record.record.container.FlacContainer.Companion6com.llfbandit.record.record.container.IContainerWriter4com.llfbandit.record.record.container.MuxerContainer2com.llfbandit.record.record.container.RawContainer3com.llfbandit.record.record.container.WaveContainer=com.llfbandit.record.record.container.WaveContainer.Companion.com.llfbandit.record.record.device.DeviceUtils8com.llfbandit.record.record.device.DeviceUtils.Companion3com.llfbandit.record.record.encoder.EncoderListener,com.llfbandit.record.record.encoder.IEncoder5com.llfbandit.record.record.encoder.MediaCodecEncoderPcom.llfbandit.record.record.encoder.MediaCodecEncoder.AudioRecorderCodecCallback<com.llfbandit.record.record.encoder.MediaCodecEncoder.Sample?com.llfbandit.record.record.encoder.MediaCodecEncoder.Companion6com.llfbandit.record.record.encoder.PassthroughEncoder,com.llfbandit.record.record.format.AacFormat.com.llfbandit.record.record.format.AmrNbFormat.com.llfbandit.record.record.format.AmrWbFormat/com.llfbandit.record.record.format.AudioFormats-com.llfbandit.record.record.format.FlacFormat)com.llfbandit.record.record.format.Format3com.llfbandit.record.record.format.Format.Companion-com.llfbandit.record.record.format.OpusFormat,com.llfbandit.record.record.format.PcmFormat-com.llfbandit.record.record.format.WaveFormat:com.llfbandit.record.record.recorder.OnAudioRecordListener2com.llfbandit.record.record.recorder.AudioRecorder<com.llfbandit.record.record.recorder.AudioRecorder.Companion.com.llfbandit.record.record.recorder.IRecorder2com.llfbandit.record.record.recorder.MediaRecorder<com.llfbandit.record.record.recorder.MediaRecorder.Companion1com.llfbandit.record.record.recorder.RecordThread>com.llfbandit.record.record.stream.RecorderRecordStreamHandler=com.llfbandit.record.record.stream.RecorderStateStreamHandlercom.llfbandit.record.Utils                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      