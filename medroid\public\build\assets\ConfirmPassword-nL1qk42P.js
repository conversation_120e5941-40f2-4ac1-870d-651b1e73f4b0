import{N as l,V as f,y as i,e as m,g as t,f as a,i as r,u as s,m as c,z as u,x as n,j as _}from"./vendor-CKE3WRFf.js";import{_ as w}from"./InputError.vue_vue_type_script_setup_true_lang-D2QV6psC.js";import{_ as C}from"./index-CviDBDnS.js";import{_ as g,a as V}from"./Label.vue_vue_type_script_setup_true_lang-CcY9peuA.js";import{L as b,_ as x}from"./AuthLayout.vue_vue_type_script_setup_true_lang-BUt3QzCv.js";import"./Primitive-B8nQAqQg.js";import"./index-BzpNoPfh.js";import"./createLucideIcon-Dy3o-9bT.js";const y={class:"space-y-6"},h={class:"grid gap-2"},N={class:"flex items-center"},z=l({__name:"ConfirmPassword",setup($){const o=f({password:""}),d=()=>{o.post(route("password.confirm"),{onFinish:()=>{o.reset()}})};return(k,e)=>(m(),i(x,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing."},{default:t(()=>[a(s(c),{title:"Confirm password"}),r("form",{onSubmit:u(d,["prevent"])},[r("div",y,[r("div",h,[a(s(g),{htmlFor:"password"},{default:t(()=>e[1]||(e[1]=[n("Password")])),_:1}),a(s(V),{id:"password",type:"password",class:"mt-1 block w-full",modelValue:s(o).password,"onUpdate:modelValue":e[0]||(e[0]=p=>s(o).password=p),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),a(w,{message:s(o).errors.password},null,8,["message"])]),r("div",N,[a(s(C),{class:"w-full",disabled:s(o).processing},{default:t(()=>[s(o).processing?(m(),i(s(b),{key:0,class:"h-4 w-4 animate-spin"})):_("",!0),e[2]||(e[2]=n(" Confirm Password "))]),_:1},8,["disabled"])])])],32)]),_:1}))}});export{z as default};
