import{_ as T}from"./AppLayout.vue_vue_type_script_setup_true_lang-Dvt6ywSf.js";import{r as F,d as l,e as o,f as x,u,m as N,g as c,i as t,t as n,s as k,j as m,F as b,q as E,y as P,P as v,x as g,W as h}from"./vendor-CKE3WRFf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B8nQAqQg.js";import"./createLucideIcon-Dy3o-9bT.js";const j={class:"flex items-center justify-between"},L={class:"flex mt-2","aria-label":"Breadcrumb"},q={class:"inline-flex items-center space-x-1 md:space-x-3"},$={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},R={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},V={class:"flex space-x-3"},W=["disabled"],X={key:0,class:"fas fa-spinner fa-spin mr-2"},z={key:1,class:"fas fa-times mr-2"},M={class:"py-12"},O={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},U={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},H={class:"p-6"},I={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},K={class:"space-y-4"},G={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},J={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},Q={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},Y={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},Z={class:"space-y-4"},_={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},tt={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},et={key:0},at={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},nt={key:1},st={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},mt={__name:"AppointmentDetail",props:{appointment:{type:Object,required:!0}},setup(a){const y=a,p=[{title:"Dashboard",href:"/dashboard"},{title:"Appointments",href:"/appointments"},{title:"Appointment Details",href:"#"}],d=F(!1),w=s=>({scheduled:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",no_show:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",A=s=>({telemedicine:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300","in-person":"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",C=()=>{h.visit(`/appointments/${y.appointment.id}/edit`)},D=async()=>{var s;if(confirm("Are you sure you want to cancel this appointment?")){d.value=!0;try{const e=(s=document.querySelector('meta[name="csrf-token"]'))==null?void 0:s.getAttribute("content"),i=await fetch(`/delete-appointment/${y.appointment.id}`,{method:"DELETE",headers:{Accept:"application/json","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":e||""},credentials:"same-origin"});if(i.ok)alert("Appointment cancelled successfully."),h.visit("/appointments");else{const r=await i.json().catch(()=>({}));alert(r.message||"Failed to cancel appointment. Please try again.")}}catch(e){console.error("Error cancelling appointment:",e),alert("Failed to cancel appointment. Please try again.")}finally{d.value=!1}}},S=s=>new Date(s).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),B=s=>new Date(`2000-01-01 ${s}`).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0});return(s,e)=>(o(),l(b,null,[x(u(N),{title:"Appointment Details"}),x(T,null,{header:c(()=>[t("div",j,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Appointment Details ",-1)),t("nav",L,[t("ol",q,[(o(),l(b,null,E(p,(i,r)=>t("li",{key:r,class:"inline-flex items-center"},[r<p.length-1?(o(),P(u(v),{key:0,href:i.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[g(n(i.title),1)]),_:2},1032,["href"])):(o(),l("span",$,n(i.title),1)),r<p.length-1?(o(),l("svg",R,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):m("",!0)])),64))])])]),t("div",V,[x(u(v),{href:"/appointments",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"},{default:c(()=>e[2]||(e[2]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),g(" Back to Appointments ")])),_:1}),a.appointment.status==="scheduled"?(o(),l("button",{key:0,onClick:C,class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"},e[3]||(e[3]=[t("i",{class:"fas fa-edit mr-2"},null,-1),g(" Edit Appointment ")]))):m("",!0),a.appointment.status==="scheduled"?(o(),l("button",{key:1,onClick:D,disabled:d.value,class:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"},[d.value?(o(),l("i",X)):(o(),l("i",z)),g(" "+n(d.value?"Cancelling...":"Cancel Appointment"),1)],8,W)):m("",!0)])])]),default:c(()=>{var i,r,f;return[t("div",M,[t("div",O,[t("div",U,[t("div",H,[t("div",I,[t("div",null,[e[10]||(e[10]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"}," Appointment Information ",-1)),t("div",K,[t("div",null,[e[4]||(e[4]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Service",-1)),t("p",G,n(((i=a.appointment.service)==null?void 0:i.name)||a.appointment.reason||"Consultation"),1)]),t("div",null,[e[5]||(e[5]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Type",-1)),t("span",{class:k([A(a.appointment.is_telemedicine?"telemedicine":"in-person"),"inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"])},n(a.appointment.is_telemedicine?"telemedicine":"in-person"),3)]),t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Status",-1)),t("span",{class:k([w(a.appointment.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"])},n(a.appointment.status),3)]),t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Date",-1)),t("p",J,n(S(a.appointment.date||a.appointment.scheduled_at)),1)]),t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Time",-1)),t("p",Q,n(B(a.appointment.time||a.appointment.scheduled_at)),1)]),t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Amount",-1)),t("p",Y,"$"+n(typeof a.appointment.amount=="number"?a.appointment.amount.toFixed(2):parseFloat(a.appointment.amount||0).toFixed(2)),1)])])]),t("div",null,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"}," Participants ",-1)),t("div",Z,[t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Patient",-1)),t("p",_,n(a.appointment.patient_name||((r=a.appointment.patient)==null?void 0:r.name)||"N/A"),1)]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Provider",-1)),t("p",tt,n(a.appointment.provider_name||((f=a.appointment.provider)==null?void 0:f.name)||"N/A"),1)]),a.appointment.notes?(o(),l("div",et,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Notes",-1)),t("p",at,n(a.appointment.notes),1)])):m("",!0),a.appointment.cancellation_reason?(o(),l("div",nt,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Cancellation Reason",-1)),t("p",st,n(a.appointment.cancellation_reason),1)])):m("",!0)])])])])])])])]}),_:1})],64))}};export{mt as default};
