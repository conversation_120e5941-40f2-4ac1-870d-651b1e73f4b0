# Development environment configuration
API_BASE_URL=http://localhost:8000/api/
API_STORAGE_URL=http://localhost:8000/

# Agora Configuration
AGORA_APP_ID=********************************
AGORA_APP_CERTIFICATE=********************************
# Leave this empty for local development to use the built-in token generator
# For production, set this to your token service URL
AGORA_TOKEN_SERVICE_URL=

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_wgyClonVuxwxR6W3xCMEWw8K
STRIPE_WEBHOOK_SECRET=http://localhost:8000
STRIPE_WEBHOOK_VERIFY=false

# App Configuration
APP_NAME=Medroid Dev
APP_VERSION=1.0.0-dev
DEBUG_MODE=true

# Feature Flags
ENABLE_SHOP=true
ENABLE_VIDEO_CONSULTATION=true
ENABLE_AI_CHAT=true