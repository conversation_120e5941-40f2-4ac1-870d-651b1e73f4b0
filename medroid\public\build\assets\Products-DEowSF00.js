import{n as R,c as p,r as i,o as q,d as l,e as r,f as y,u as m,m as K,g,i as e,l as B,v as Q,F as h,q as $,t as o,p as D,s as L,x,j as v,y as N,P as k}from"./vendor-CKE3WRFf.js";import{_ as G}from"./AppLayout.vue_vue_type_script_setup_true_lang-Dvt6ywSf.js";import{_ as H}from"./BulkImportModal-DI1Em9AY.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B8nQAqQg.js";import"./createLucideIcon-Dy3o-9bT.js";const J={class:"flex items-center justify-between"},O={class:"flex mt-2","aria-label":"Breadcrumb"},W={class:"inline-flex items-center space-x-1 md:space-x-3"},X={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},Y={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},Z={key:0,class:"flex space-x-3"},ee={class:"py-12"},te={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},se={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ae={class:"p-6"},re={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},le=["value"],oe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},de={class:"p-6 text-gray-900 dark:text-gray-100"},ne={key:0,class:"text-center py-8"},ie={key:1,class:"text-center py-8"},ue={key:2,class:"overflow-x-auto"},ce={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},pe={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ge={class:"px-6 py-4 whitespace-nowrap"},xe={class:"flex items-center"},ye={class:"flex-shrink-0 h-10 w-10"},me=["src","alt"],ve={key:1,class:"h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},fe={class:"ml-4"},he={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ke={class:"text-sm text-gray-500 dark:text-gray-400"},be={class:"px-6 py-4 whitespace-nowrap"},_e={class:"text-sm text-gray-900 dark:text-gray-100"},we={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"px-6 py-4 whitespace-nowrap"},Pe={class:"text-sm text-gray-900 dark:text-gray-100"},Se={key:0,class:"text-xs text-gray-500 line-through ml-1"},Be={class:"px-6 py-4 whitespace-nowrap"},$e={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Me={key:1,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Ie={__name:"Products",setup(Ve){const T=R(),f=p(()=>{var a;return(a=T.props.auth)==null?void 0:a.user}),b=[{title:"Dashboard",href:"/dashboard"},{title:"My Products",href:"/provider/products"}],_=i(!1),w=i([]),M=i([]),n=i(""),u=i("all"),c=i("all"),C=i(!1),P=async()=>{var a;_.value=!0;try{const t=new URLSearchParams;n.value&&t.append("search",n.value),u.value!=="all"&&t.append("category",u.value),c.value!=="all"&&t.append("type",c.value);const s=await window.axios.get(`/provider/products-list?${t.toString()}`);w.value=((a=s.data.products)==null?void 0:a.data)||s.data.products||[],s.data.categories&&(M.value=s.data.categories)}catch(t){console.error("Error fetching products:",t),w.value=[]}finally{_.value=!1}},U=a=>a?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",I=a=>a==="digital"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",V=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),A=p(()=>w.value.filter(a=>{const t=!n.value||a.name.toLowerCase().includes(n.value.toLowerCase())||a.sku.toLowerCase().includes(n.value.toLowerCase()),s=u.value==="all"||a.category_id==u.value,d=c.value==="all"||a.type===c.value;return t&&s&&d})),S=p(()=>{var a,t;return((t=(a=f.value)==null?void 0:a.roles)==null?void 0:t.some(s=>s.name==="admin"))||!1}),j=p(()=>{var a,t;return S.value||((t=(a=f.value)==null?void 0:a.user_permissions)==null?void 0:t.includes("create products"))||!1}),E=p(()=>{var a,t;return S.value||((t=(a=f.value)==null?void 0:a.user_permissions)==null?void 0:t.includes("edit products"))||!1}),F=p(()=>{var a,t;return S.value||((t=(a=f.value)==null?void 0:a.user_permissions)==null?void 0:t.includes("delete products"))||!1}),z=a=>{alert(`Successfully imported ${a.imported_count} products!`),P()};return q(()=>{P()}),(a,t)=>(r(),l(h,null,[y(m(K),{title:"My Products"}),y(G,null,{header:g(()=>[e("div",J,[e("div",null,[t[6]||(t[6]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," My Products ",-1)),e("nav",O,[e("ol",W,[(r(),l(h,null,$(b,(s,d)=>e("li",{key:d,class:"inline-flex items-center"},[d<b.length-1?(r(),N(m(k),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:g(()=>[x(o(s.title),1)]),_:2},1032,["href"])):(r(),l("span",X,o(s.title),1)),d<b.length-1?(r(),l("svg",Y,t[5]||(t[5]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):v("",!0)])),64))])])]),j.value?(r(),l("div",Z,[e("button",{onClick:t[0]||(t[0]=s=>C.value=!0),class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"},t[7]||(t[7]=[e("i",{class:"fas fa-upload mr-2"},null,-1),x(" Bulk Import ")])),y(m(k),{href:"/provider/products/create",class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:g(()=>t[8]||(t[8]=[e("i",{class:"fas fa-plus mr-2"},null,-1),x(" Add Product ")])),_:1})])):v("",!0)])]),default:g(()=>[e("div",ee,[e("div",te,[e("div",se,[e("div",ae,[e("div",re,[e("div",null,[B(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>n.value=s),type:"text",placeholder:"Search products...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[Q,n.value]])]),e("div",null,[B(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>u.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[t[9]||(t[9]=e("option",{value:"all"},"All Categories",-1)),(r(!0),l(h,null,$(M.value,s=>(r(),l("option",{key:s.id,value:s.id},o(s.name),9,le))),128))],512),[[D,u.value]])]),e("div",null,[B(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>c.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},t[10]||(t[10]=[e("option",{value:"all"},"All Types",-1),e("option",{value:"physical"},"Physical",-1),e("option",{value:"digital"},"Digital",-1)]),512),[[D,c.value]])]),e("div",null,[e("button",{onClick:P,class:"w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Refresh ")])])])]),e("div",oe,[e("div",de,[_.value?(r(),l("div",ne,t[11]||(t[11]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):A.value.length===0?(r(),l("div",ie,t[12]||(t[12]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"No products found.",-1)]))):(r(),l("div",ue,[e("table",ce,[t[16]||(t[16]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Product "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Category "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Type "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Price "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",pe,[(r(!0),l(h,null,$(A.value,s=>{var d;return r(),l("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",ge,[e("div",xe,[e("div",ye,[s.primary_image?(r(),l("img",{key:0,src:`/storage/${s.primary_image}`,alt:s.name,class:"h-10 w-10 rounded object-cover"},null,8,me)):(r(),l("div",ve,t[13]||(t[13]=[e("i",{class:"fas fa-box text-gray-500 dark:text-gray-400"},null,-1)])))]),e("div",fe,[e("div",he,o(s.name),1),e("div",ke," SKU: "+o(s.sku),1)])])]),e("td",be,[e("span",_e,o(((d=s.category)==null?void 0:d.name)||"N/A"),1)]),e("td",we,[e("span",{class:L([I(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(s.type),3)]),e("td",Ce,[e("div",Pe,[x(o(V(s.price))+" ",1),s.sale_price?(r(),l("span",Se,o(V(s.sale_price)),1)):v("",!0)])]),e("td",Be,[e("span",{class:L([U(s.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(s.is_active?"Active":"Inactive"),3)]),e("td",$e,[y(m(k),{href:`/provider/products/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:g(()=>t[14]||(t[14]=[x(" View ")])),_:2},1032,["href"]),E.value?(r(),N(m(k),{key:0,href:`/provider/products/${s.id}/edit`,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},{default:g(()=>t[15]||(t[15]=[x(" Edit ")])),_:2},1032,["href"])):v("",!0),F.value?(r(),l("button",Me," Delete ")):v("",!0)])])}),128))])])]))])])])]),y(H,{"is-open":C.value,onClose:t[4]||(t[4]=s=>C.value=!1),onImported:z},null,8,["is-open"])]),_:1})],64))}};export{Ie as default};
