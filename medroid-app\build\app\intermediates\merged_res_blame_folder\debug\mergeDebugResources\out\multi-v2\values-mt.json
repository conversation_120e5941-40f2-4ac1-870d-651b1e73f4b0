{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-mt/values-mt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-mt\\values-mt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,194,258,343,406,475,534,617,693,761,821", "endColumns": "79,58,63,84,62,68,58,82,75,67,59,72", "endOffsets": "130,189,253,338,401,470,529,612,688,756,816,889"}, "to": {"startLines": "17,27,29,30,31,35,43,46,49,53,57,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1207,2068,2215,2279,2364,2658,3187,3379,3645,4001,4375,4669", "endColumns": "79,58,63,84,62,68,58,82,75,67,59,72", "endOffsets": "1282,2122,2274,2359,2422,2722,3241,3457,3716,4064,4430,4737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-mt\\values-mt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,212,279,363,434,495,567,634,696,764,834,894,956,1030,1094,1164,1227,1301,1365,1446,1523,1617,1717,1845,1937,1985,2032,2129,2193,2261,2339,2454,2550,2659", "endColumns": "65,90,66,83,70,60,71,66,61,67,69,59,61,73,63,69,62,73,63,80,76,93,99,127,91,47,46,96,63,67,77,114,95,108,109", "endOffsets": "116,207,274,358,429,490,562,629,691,759,829,889,951,1025,1089,1159,1222,1296,1360,1441,1518,1612,1712,1840,1932,1980,2027,2124,2188,2256,2334,2449,2545,2654,2764"}, "to": {"startLines": "16,19,22,24,25,26,33,34,36,37,38,39,40,41,42,44,45,48,59,60,72,74,75,102,103,113,123,124,126,133,134,143,144,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1141,1396,1691,1852,1936,2007,2519,2591,2727,2789,2857,2927,2987,3049,3123,3246,3316,3571,4524,4588,5748,5920,6014,9826,9954,11030,11754,11801,11951,12739,12807,13589,13704,14449,14558", "endColumns": "65,90,66,83,70,60,71,66,61,67,69,59,61,73,63,69,62,73,63,80,76,93,99,127,91,47,46,96,63,67,77,114,95,108,109", "endOffsets": "1202,1482,1753,1931,2002,2063,2586,2653,2784,2852,2922,2982,3044,3118,3182,3311,3374,3640,4583,4664,5820,6009,6109,9949,10041,11073,11796,11893,12010,12802,12880,13699,13795,14553,14663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-mt\\values-mt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,240,367,486,575,692,861,1060,1225,1311,1429,1526,1646,1739,1800,1867,1965,2081,2298,2375,2460,2532,2625,2741,2838,2934,3001,3061,3167,3276,3402,3506,3577,3654,3738,3811,3933,4061", "endColumns": "86,97,126,118,88,116,168,198,164,85,117,96,119,92,60,66,97,115,216,76,84,71,92,115,96,95,66,59,105,108,125,103,70,76,83,72,121,127,91", "endOffsets": "137,235,362,481,570,687,856,1055,1220,1306,1424,1521,1641,1734,1795,1862,1960,2076,2293,2370,2455,2527,2620,2736,2833,2929,2996,3056,3162,3271,3397,3501,3572,3649,3733,3806,3928,4056,4148"}, "to": {"startLines": "10,88,105,115,116,163,169,170,171,172,174,175,176,178,179,180,181,182,183,184,185,186,187,188,193,194,195,196,197,198,199,200,201,219,226,227,228,229,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "644,7975,10103,11126,11245,15415,15884,16053,16252,16417,16612,16730,16827,17036,17129,17190,17257,17355,17471,17688,17765,17850,17922,18015,18468,18565,18661,18728,18788,18894,19003,19129,19233,21817,22332,22416,22489,22611,22739", "endColumns": "86,97,126,118,88,116,168,198,164,85,117,96,119,92,60,66,97,115,216,76,84,71,92,115,96,95,66,59,105,108,125,103,70,76,83,72,121,127,91", "endOffsets": "726,8068,10225,11240,11329,15527,16048,16247,16412,16498,16725,16822,16942,17124,17185,17252,17350,17466,17683,17760,17845,17917,18010,18126,18560,18656,18723,18783,18889,18998,19124,19228,19299,21889,22411,22484,22606,22734,22826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-mt\\values-mt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,203,322,381,479,535,597,673,784,961,1049,1126,1220,1310,1441,1591,1662,1741,1904,1973,2032,2109,2184,2345,2455,2523,2624,2708,2788,2874,2944", "endColumns": "69,77,118,58,97,55,61,75,110,176,87,76,93,89,130,149,70,78,162,68,58,76,74,160,109,67,100,83,79,85,69,161", "endOffsets": "120,198,317,376,474,530,592,668,779,956,1044,1121,1215,1305,1436,1586,1657,1736,1899,1968,2027,2104,2179,2340,2450,2518,2619,2703,2783,2869,2939,3101"}, "to": {"startLines": "9,11,145,161,203,214,215,216,217,218,231,232,233,234,235,236,237,238,240,241,242,243,244,245,246,247,248,249,250,251,252,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "574,731,13800,15288,19526,21335,21391,21453,21529,21640,22831,22919,22996,23090,23180,23311,23461,23532,23696,23859,23928,23987,24064,24139,24300,24410,24478,24579,24663,24743,24829,24899", "endColumns": "69,77,118,58,97,55,61,75,110,176,87,76,93,89,130,149,70,78,162,68,58,76,74,160,109,67,100,83,79,85,69,161", "endOffsets": "639,804,13914,15342,19619,21386,21448,21524,21635,21812,22914,22991,23085,23175,23306,23456,23527,23606,23854,23923,23982,24059,24134,24295,24405,24473,24574,24658,24738,24824,24894,25056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-mt\\values-mt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,355,440,504,574,625,719,808,906,1015,1124,1219,1313,1401,1493,1602,1707,1780,1882,1979,2069,2188,2277,2379,2459,2568,2652,2767,2869,2967,3070,3191,3283,3378,3491,3564,3628,4346,5058,5138,5254,5365,5420,5514,5604,5678,5785,5892,5949,6030,6083,6163,6279,6363,6435,6483,6566,6664,6713,6760,6832,6890,6953,7165,7347,7484,7549,7644,7713,7805,7899,7998,8083,8199,8286,8365,8455,8510,8645,8693,8747,8813,8874,8950,9018,9090,9196", "endColumns": "74,83,61,78,84,63,69,50,93,88,97,108,108,94,93,87,91,108,104,72,101,96,89,118,88,101,79,108,83,114,101,97,102,120,91,94,112,72,63,717,711,79,115,110,54,93,89,73,106,106,56,80,52,79,115,83,71,47,82,97,48,46,71,57,62,211,181,136,64,94,68,91,93,98,84,115,86,78,89,54,134,47,53,65,60,75,67,71,105,84", "endOffsets": "125,209,271,350,435,499,569,620,714,803,901,1010,1119,1214,1308,1396,1488,1597,1702,1775,1877,1974,2064,2183,2272,2374,2454,2563,2647,2762,2864,2962,3065,3186,3278,3373,3486,3559,3623,4341,5053,5133,5249,5360,5415,5509,5599,5673,5780,5887,5944,6025,6078,6158,6274,6358,6430,6478,6561,6659,6708,6755,6827,6885,6948,7160,7342,7479,7544,7639,7708,7800,7894,7993,8078,8194,8281,8360,8450,8505,8640,8688,8742,8808,8869,8945,9013,9085,9191,9276"}, "to": {"startLines": "2,3,4,5,6,7,8,12,13,14,15,18,20,21,23,28,32,47,50,51,52,54,55,56,58,62,63,64,65,66,67,68,69,70,71,73,76,78,79,80,89,90,91,92,93,94,95,96,97,98,99,100,107,108,109,110,111,114,119,120,121,122,127,128,129,130,131,132,136,137,146,147,149,150,154,155,157,166,167,204,205,206,207,211,220,221,222,223,224,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,355,440,504,809,860,954,1043,1287,1487,1596,1758,2127,2427,3462,3721,3826,3899,4069,4166,4256,4435,4742,4844,4924,5033,5117,5232,5334,5432,5535,5656,5825,6114,6417,6490,6554,8073,8785,8865,8981,9092,9147,9241,9331,9405,9512,9619,9676,10294,10347,10427,10543,10627,11078,11477,11560,11658,11707,12015,12087,12145,12208,12420,12602,12938,13003,13919,13988,14170,14264,14668,14753,14939,15655,15734,19624,19679,19814,19862,20322,21894,21955,22031,22099,22171,23611", "endColumns": "74,83,61,78,84,63,69,50,93,88,97,108,108,94,93,87,91,108,104,72,101,96,89,118,88,101,79,108,83,114,101,97,102,120,91,94,112,72,63,717,711,79,115,110,54,93,89,73,106,106,56,80,52,79,115,83,71,47,82,97,48,46,71,57,62,211,181,136,64,94,68,91,93,98,84,115,86,78,89,54,134,47,53,65,60,75,67,71,105,84", "endOffsets": "125,209,271,350,435,499,569,855,949,1038,1136,1391,1591,1686,1847,2210,2514,3566,3821,3894,3996,4161,4251,4370,4519,4839,4919,5028,5112,5227,5329,5427,5530,5651,5743,5915,6222,6485,6549,7267,8780,8860,8976,9087,9142,9236,9326,9400,9507,9614,9671,9752,10342,10422,10538,10622,10694,11121,11555,11653,11702,11749,12082,12140,12203,12415,12597,12734,12998,13093,13983,14075,14259,14358,14748,14864,15021,15729,15819,19674,19809,19857,19911,20383,21950,22026,22094,22166,22272,23691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-mt\\values-mt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,245,290,360,466,539,785,850,948,1017,1074,1138,1469,1548,1612,1665,1718,1801,1927,2055,2115,2209,2299,2385,2455,2560,2640,2717,2785,2845,2908,2968,3077,3166,3265,3338,3417,3503,3725,3952,4070,4131,5013,5078", "endColumns": "189,44,69,105,72,245,64,97,68,56,63,330,78,63,52,52,82,125,127,59,93,89,85,69,104,79,76,67,59,62,59,108,88,98,72,78,85,221,226,117,60,881,64,54", "endOffsets": "240,285,355,461,534,780,845,943,1012,1069,1133,1464,1543,1607,1660,1713,1796,1922,2050,2110,2204,2294,2380,2450,2555,2635,2712,2780,2840,2903,2963,3072,3161,3260,3333,3412,3498,3720,3947,4065,4126,5008,5073,5128"}, "to": {"startLines": "77,81,82,83,84,85,86,87,101,104,106,112,117,118,125,135,138,139,140,141,142,148,151,156,158,159,160,162,164,165,168,173,177,189,190,191,192,202,208,209,210,212,213,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6227,7272,7317,7387,7493,7566,7812,7877,9757,10046,10230,10699,11334,11413,11898,12885,13098,13181,13307,13435,13495,14080,14363,14869,15026,15131,15211,15347,15532,15592,15824,16503,16947,18131,18230,18303,18382,19304,19916,20143,20261,20388,21270,22277", "endColumns": "189,44,69,105,72,245,64,97,68,56,63,330,78,63,52,52,82,125,127,59,93,89,85,69,104,79,76,67,59,62,59,108,88,98,72,78,85,221,226,117,60,881,64,54", "endOffsets": "6412,7312,7382,7488,7561,7807,7872,7970,9821,10098,10289,11025,11408,11472,11946,12933,13176,13302,13430,13490,13584,14165,14444,14934,15126,15206,15283,15410,15587,15650,15879,16607,17031,18225,18298,18377,18463,19521,20138,20256,20317,21265,21330,22327"}}]}]}