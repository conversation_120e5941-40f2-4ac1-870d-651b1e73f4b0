<?php

/**
 * Quick Fix Script for Route Issue
 * Run this directly on the production server
 */

echo "MEDROID QUICK ROUTE FIX\n";
echo "======================\n\n";

// Change to the correct directory
$laravelRoot = __DIR__;
chdir($laravelRoot);

echo "Working directory: " . getcwd() . "\n\n";

// Function to execute artisan commands
function execArtisan($command) {
    $fullCommand = "php artisan $command 2>&1";
    echo "Executing: $fullCommand\n";
    
    $output = [];
    $returnCode = 0;
    exec($fullCommand, $output, $returnCode);
    
    foreach ($output as $line) {
        echo "  $line\n";
    }
    
    if ($returnCode === 0) {
        echo "✓ Success\n\n";
    } else {
        echo "✗ Failed (code: $returnCode)\n\n";
    }
    
    return $returnCode === 0;
}

// Step 1: Clear all caches
echo "1. CLEARING CACHES\n";
echo "==================\n";

execArtisan('cache:clear');
execArtisan('config:clear');
execArtisan('route:clear');
execArtisan('view:clear');

// Remove cache files manually
$cacheFiles = [
    'bootstrap/cache/routes-v7.php',
    'bootstrap/cache/compiled.php',
    'bootstrap/cache/services.php',
    'bootstrap/cache/packages.php'
];

foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "✓ Removed: $file\n";
    }
}

echo "\n";

// Step 2: Check route file
echo "2. CHECKING ROUTE FILE\n";
echo "======================\n";

$webRoutesFile = 'routes/web.php';
if (file_exists($webRoutesFile)) {
    $content = file_get_contents($webRoutesFile);
    
    if (strpos($content, "web-api/chat") !== false) {
        echo "✓ Found web-api/chat routes\n";
        
        if (strpos($content, "Route::post('start'") !== false) {
            echo "✓ Found start route\n";
        } else {
            echo "✗ Start route missing\n";
        }
    } else {
        echo "✗ web-api/chat routes missing\n";
    }
} else {
    echo "✗ routes/web.php not found\n";
}

echo "\n";

// Step 3: Re-cache for production
echo "3. RE-CACHING FOR PRODUCTION\n";
echo "============================\n";

execArtisan('config:cache');
execArtisan('route:cache');
execArtisan('view:cache');

// Step 4: Test route registration
echo "4. TESTING ROUTE REGISTRATION\n";
echo "=============================\n";

$output = [];
exec('php artisan route:list | grep "web-api/chat/start"', $output);

if (!empty($output)) {
    echo "✓ Route found:\n";
    foreach ($output as $line) {
        echo "  $line\n";
    }
} else {
    echo "✗ Route not found in route list\n";
    
    // Show all web-api routes
    $output = [];
    exec('php artisan route:list | grep "web-api"', $output);
    
    if (!empty($output)) {
        echo "\nFound other web-api routes:\n";
        foreach ($output as $line) {
            echo "  $line\n";
        }
    }
}

echo "\n";

// Step 5: Check controller
echo "5. CHECKING CONTROLLER\n";
echo "======================\n";

$controllerFile = 'app/Http/Controllers/AIChatController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    
    if (strpos($content, 'function startConversation') !== false) {
        echo "✓ startConversation method found\n";
    } else {
        echo "✗ startConversation method missing\n";
    }
} else {
    echo "✗ AIChatController.php not found\n";
}

echo "\n";

// Step 6: Final instructions
echo "6. FINAL STEPS\n";
echo "==============\n";

echo "If you're on a production server, also run:\n\n";

echo "# Restart PHP-FPM (choose the correct version)\n";
echo "sudo systemctl restart php8.1-fpm\n";
echo "# OR\n";
echo "sudo systemctl restart php8.0-fpm\n\n";

echo "# Restart web server\n";
echo "sudo systemctl restart nginx\n";
echo "# OR\n";
echo "sudo systemctl restart apache2\n\n";

echo "# Test the endpoint\n";
echo "curl -X POST https://app.medroid.ai/web-api/chat/start \\\n";
echo "     -H 'Content-Type: application/json' \\\n";
echo "     -H 'Accept: application/json' \\\n";
echo "     -d '{}'\n\n";

echo "# Check logs if still not working\n";
echo "tail -f storage/logs/laravel.log\n\n";

echo "QUICK FIX COMPLETED!\n";
echo "====================\n";
