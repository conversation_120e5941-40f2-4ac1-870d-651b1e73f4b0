{"logs": [{"outputFile": "com.example.medroid_app-mergeDebugResources-123:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5794", "endColumns": "144", "endOffsets": "5934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,13319", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,13400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de6d39d460bdc21c935fb84db93695ba\\transformed\\jetified-payments-ui-core-20.34.4\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,208,255,326,427,501,721,781,873,945,1002,1066,1386,1458,1525,1579,1636,1720,1852,1966,2024,2113,2194,2283,2349,2450,2527,2605,2668,2729,2791,2852,2952,3042,3145,3220,3299,3385,3599,3810,3925,3981,4633,4698", "endColumns": "152,46,70,100,73,219,59,91,71,56,63,319,71,66,53,56,83,131,113,57,88,80,88,65,100,76,77,62,60,61,60,99,89,102,74,78,85,213,210,114,55,651,64,54", "endOffsets": "203,250,321,422,496,716,776,868,940,997,1061,1381,1453,1520,1574,1631,1715,1847,1961,2019,2108,2189,2278,2344,2445,2522,2600,2663,2724,2786,2847,2947,3037,3140,3215,3294,3380,3594,3805,3920,3976,4628,4693,4748"}, "to": {"startLines": "230,234,235,236,237,238,239,240,254,257,259,265,270,271,278,288,291,292,293,294,295,301,304,309,311,312,313,315,317,318,321,326,330,342,343,344,345,355,361,362,363,365,366,378", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19912,20890,20937,21008,21109,21183,21403,21463,23269,23538,23733,24178,24788,24860,25329,26260,26468,26552,26684,26798,26856,27397,27637,28063,28202,28303,28380,28514,28692,28753,28996,29643,30043,31197,31300,31375,31454,32366,32945,33156,33271,33394,34046,35050", "endColumns": "152,46,70,100,73,219,59,91,71,56,63,319,71,66,53,56,83,131,113,57,88,80,88,65,100,76,77,62,60,61,60,99,89,102,74,78,85,213,210,114,55,651,64,54", "endOffsets": "20060,20932,21003,21104,21178,21398,21458,21550,23336,23590,23792,24493,24855,24922,25378,26312,26547,26679,26793,26851,26940,27473,27721,28124,28298,28375,28453,28572,28748,28810,29052,29738,30128,31295,31370,31449,31535,32575,33151,33266,33322,34041,34106,35100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65dd303e606b58b58269b42f69e5f89\\transformed\\jetified-stripe-core-20.34.4\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,198,260,342,406,479,538,609,684,752,814", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "132,193,255,337,401,474,533,604,679,747,809,881"}, "to": {"startLines": "170,180,182,183,184,188,196,199,202,206,210,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15219,16037,16179,16241,16323,16611,17143,17334,17574,17908,18260,18557", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "15296,16093,16236,16318,16382,16679,17197,17400,17644,17971,18317,18624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,147", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,13481", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,13577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be80f888898887bb71616e48bf7874ba\\transformed\\jetified-ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "48,49,71,72,74,84,85,136,137,139,140,143,144,146,408,409,410", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4620,7229,7326,7507,8367,8450,12518,12609,12775,12847,13144,13229,13405,37898,37974,38041", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4615,4701,7321,7420,7588,8445,8542,12604,12691,12842,12911,13224,13314,13476,37969,38036,38149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e71e7c08b297cb32539b89f832f53d8\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,213", "endColumns": "75,81,73", "endOffsets": "126,208,282"}, "to": {"startLines": "50,73,77", "startColumns": "4,4,4", "startOffsets": "4706,7425,7744", "endColumns": "75,81,73", "endOffsets": "4777,7502,7813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a502440b69382cbb13af31a1d5985f9f\\transformed\\material-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1066,1158,1237,1297,1387,1451,1522,1585,1660,1724,1778,1905,1963,2025,2079,2158,2299,2386,2468,2607,2690,2774,2861,2917,2968,3034,3108,3188,3275,3348,3425,3494,3568,3656,3733,3826,3922,3996,4076,4173,4225,4291,4378,4466,4528,4592,4655,4767,4876,4983,5093,5153,5208", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,86,55,50,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,59,54,76", "endOffsets": "268,349,427,511,606,695,796,916,997,1061,1153,1232,1292,1382,1446,1517,1580,1655,1719,1773,1900,1958,2020,2074,2153,2294,2381,2463,2602,2685,2769,2856,2912,2963,3029,3103,3183,3270,3343,3420,3489,3563,3651,3728,3821,3917,3991,4071,4168,4220,4286,4373,4461,4523,4587,4650,4762,4871,4978,5088,5148,5203,5280"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,7593,7818,8288,8547,8607,8697,8761,8832,8895,8970,9034,9088,9215,9273,9335,9389,9468,9609,9696,9778,9917,10000,10084,10171,10227,10278,10344,10418,10498,10585,10658,10735,10804,10878,10966,11043,11136,11232,11306,11386,11483,11535,11601,11688,11776,11838,11902,11965,12077,12186,12293,12403,12463,12916", "endLines": "5,33,34,35,36,37,45,46,47,75,78,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,141", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,86,55,50,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,59,54,76", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,7652,7905,8362,8602,8692,8756,8827,8890,8965,9029,9083,9210,9268,9330,9384,9463,9604,9691,9773,9912,9995,10079,10166,10222,10273,10339,10413,10493,10580,10653,10730,10799,10873,10961,11038,11131,11227,11301,11381,11478,11530,11596,11683,11771,11833,11897,11960,12072,12181,12288,12398,12458,12513,12988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dd497b801a534c8e6518183be54f249b\\transformed\\jetified-link-20.34.4\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,215,327,383,475,535,598,676,794,963,1039,1112,1200,1287,1401,1534,1602,1673,1836,1906,1963,2040,2119,2246,2372,2441,2555,2643,2720,2804,2875", "endColumns": "76,82,111,55,91,59,62,77,117,168,75,72,87,86,113,132,67,70,162,69,56,76,78,126,125,68,113,87,76,83,70,163", "endOffsets": "127,210,322,378,470,530,593,671,789,958,1034,1107,1195,1282,1396,1529,1597,1668,1831,1901,1958,2035,2114,2241,2367,2436,2550,2638,2715,2799,2870,3034"}, "to": {"startLines": "162,164,298,314,356,367,368,369,370,371,384,385,386,387,388,389,390,391,393,394,395,396,397,398,399,400,401,402,403,404,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14619,14788,27121,28458,32580,34111,34171,34234,34312,34430,35574,35650,35723,35811,35898,36012,36145,36213,36363,36526,36596,36653,36730,36809,36936,37062,37131,37245,37333,37410,37494,37565", "endColumns": "76,82,111,55,91,59,62,77,117,168,75,72,87,86,113,132,67,70,162,69,56,76,78,126,125,68,113,87,76,83,70,163", "endOffsets": "14691,14866,27228,28509,32667,34166,34229,34307,34425,34594,35645,35718,35806,35893,36007,36140,36208,36279,36521,36591,36648,36725,36804,36931,37057,37126,37240,37328,37405,37489,37560,37724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f779a58ef2338aeb71ccad028cde32d\\transformed\\jetified-stripe-3ds2-android-6.1.7\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,250,317,385,451,525", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "137,245,312,380,446,520,589"}, "to": {"startLines": "148,149,150,151,152,153,154", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13582,13669,13777,13844,13912,13978,14052", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "13664,13772,13839,13907,13973,14047,14116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "69,76,138,142,407,411,412", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7044,7657,12696,12993,37729,38154,38241", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "7109,7739,12770,13139,37893,38236,38317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e71634029c28cc8731cc9cadb4b93d05\\transformed\\jetified-paymentsheet-20.34.4\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,147,241,379,480,571,686,857,1040,1180,1272,1378,1473,1572,1673,1734,1800,1900,2010,2223,2297,2382,2453,2521,2636,2723,2820,2889,2949,3054,3158,3288,3391,3462,3534,3619,3691,3803,3916", "endColumns": "91,93,137,100,90,114,170,182,139,91,105,94,98,100,60,65,99,109,212,73,84,70,67,114,86,96,68,59,104,103,129,102,70,71,84,71,111,112,86", "endOffsets": "142,236,374,475,566,681,852,1035,1175,1267,1373,1468,1567,1668,1729,1795,1895,2005,2218,2292,2377,2448,2516,2631,2718,2815,2884,2944,3049,3153,3283,3386,3457,3529,3614,3686,3798,3911,3998"}, "to": {"startLines": "163,241,258,268,269,316,322,323,324,325,327,328,329,331,332,333,334,335,336,337,338,339,340,341,346,347,348,349,350,351,352,353,354,372,379,380,381,382,383", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14696,21555,23595,24596,24697,28577,29057,29228,29411,29551,29743,29849,29944,30133,30234,30295,30361,30461,30571,30784,30858,30943,31014,31082,31540,31627,31724,31793,31853,31958,32062,32192,32295,34599,35105,35190,35262,35374,35487", "endColumns": "91,93,137,100,90,114,170,182,139,91,105,94,98,100,60,65,99,109,212,73,84,70,67,114,86,96,68,59,104,103,129,102,70,71,84,71,111,112,86", "endOffsets": "14783,21644,23728,24692,24783,28687,29223,29406,29546,29638,29844,29939,30038,30229,30290,30356,30456,30566,30779,30853,30938,31009,31077,31192,31622,31719,31788,31848,31953,32057,32187,32290,32361,34666,35185,35257,35369,35482,35569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\763651434abe5bd823dd14f089cde7f1\\transformed\\jetified-stripe-ui-core-20.34.4\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,203,271,357,428,489,562,629,691,759,829,889,950,1024,1088,1157,1220,1298,1363,1451,1531,1610,1696,1802,1893,1943,1991,2066,2130,2192,2261,2348,2437,2533", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "116,198,266,352,423,484,557,624,686,754,824,884,945,1019,1083,1152,1215,1293,1358,1446,1526,1605,1691,1797,1888,1938,1986,2061,2125,2187,2256,2343,2432,2528,2609"}, "to": {"startLines": "169,172,175,177,178,179,186,187,189,190,191,192,193,194,195,197,198,201,212,213,225,227,228,255,256,266,276,277,279,286,287,296,297,305,306", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15153,15403,15673,15819,15905,15976,16471,16544,16684,16746,16814,16884,16944,17005,17079,17202,17271,17496,18404,18469,19488,19645,19724,23341,23447,24498,25206,25254,25383,26129,26191,26945,27032,27726,27822", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "15214,15480,15736,15900,15971,16032,16539,16606,16741,16809,16879,16939,17000,17074,17138,17266,17329,17569,18464,18552,19563,19719,19805,23442,23533,24543,25249,25324,25442,26186,26255,27027,27116,27817,27898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4782,4887,5035,5162,5270,5437,5567,5689,5939,6109,6217,6381,6511,6668,6825,6894,6960", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "4882,5030,5157,5265,5432,5562,5684,5789,6104,6212,6376,6506,6663,6820,6889,6955,7039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635aae3c1d96b84c5b5820d0f3468bfb\\transformed\\jetified-payments-core-20.34.4\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,494,553,607,683,756,835,937,1039,1125,1203,1284,1368,1459,1554,1626,1718,1806,1894,2002,2084,2176,2255,2354,2428,2514,2601,2684,2767,2870,2943,3020,3122,3192,3257,3947,4612,4690,4807,4922,4977,5071,5157,5229,5319,5416,5473,5567,5618,5696,5807,5878,5948,5996,6080,6178,6228,6275,6340,6398,6461,6653,6818,6957,7022,7108,7183,7272,7354,7431,7500,7591,7664,7750,7845,7898,8014,8064,8118,8185,8257,8330,8399,8474,8564", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,47,83,97,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,85,94,52,115,49,53,66,71,72,68,74,89,78", "endOffsets": "124,207,269,343,426,489,548,602,678,751,830,932,1034,1120,1198,1279,1363,1454,1549,1621,1713,1801,1889,1997,2079,2171,2250,2349,2423,2509,2596,2679,2762,2865,2938,3015,3117,3187,3252,3942,4607,4685,4802,4917,4972,5066,5152,5224,5314,5411,5468,5562,5613,5691,5802,5873,5943,5991,6075,6173,6223,6270,6335,6393,6456,6648,6813,6952,7017,7103,7178,7267,7349,7426,7495,7586,7659,7745,7840,7893,8009,8059,8113,8180,8252,8325,8394,8469,8559,8638"}, "to": {"startLines": "155,156,157,158,159,160,161,165,166,167,168,171,173,174,176,181,185,200,203,204,205,207,208,209,211,215,216,217,218,219,220,221,222,223,224,226,229,231,232,233,242,243,244,245,246,247,248,249,250,251,252,253,260,261,262,263,264,267,272,273,274,275,280,281,282,283,284,285,289,290,299,300,302,303,307,308,310,319,320,357,358,359,360,364,373,374,375,376,377,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14121,14195,14278,14340,14414,14497,14560,14871,14925,15001,15074,15301,15485,15587,15741,16098,16387,17405,17649,17744,17816,17976,18064,18152,18322,18629,18721,18800,18899,18973,19059,19146,19229,19312,19415,19568,19810,20065,20135,20200,21649,22314,22392,22509,22624,22679,22773,22859,22931,23021,23118,23175,23797,23848,23926,24037,24108,24548,24927,25011,25109,25159,25447,25512,25570,25633,25825,25990,26317,26382,27233,27308,27478,27560,27903,27972,28129,28815,28901,32672,32725,32841,32891,33327,34671,34743,34816,34885,34960,36284", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,47,83,97,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,85,94,52,115,49,53,66,71,72,68,74,89,78", "endOffsets": "14190,14273,14335,14409,14492,14555,14614,14920,14996,15069,15148,15398,15582,15668,15814,16174,16466,17491,17739,17811,17903,18059,18147,18255,18399,18716,18795,18894,18968,19054,19141,19224,19307,19410,19483,19640,19907,20130,20195,20885,22309,22387,22504,22619,22674,22768,22854,22926,23016,23113,23170,23264,23843,23921,24032,24103,24173,24591,25006,25104,25154,25201,25507,25565,25628,25820,25985,26124,26377,26463,27303,27392,27555,27632,27967,28058,28197,28896,28991,32720,32836,32886,32940,33389,34738,34811,34880,34955,35045,36358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ba2bbf823ab5a18be680588e75f7e6ba\\transformed\\jetified-play-services-wallet-19.2.1\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "82,413", "startColumns": "4,4", "startOffsets": "8227,38322", "endColumns": "60,79", "endOffsets": "8283,38397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "70,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "7114,7910,8009,8121", "endColumns": "114,98,111,105", "endOffsets": "7224,8004,8116,8222"}}]}]}