import{n as z,c as m,r as c,o as F,d,e as l,f as _,u as k,m as j,g as y,i as e,j as D,t as r,l as S,v as $,p as O,F as C,q as B,s as N,P as V,x as U,y as E}from"./vendor-CKE3WRFf.js";import{_ as I}from"./AppLayout.vue_vue_type_script_setup_true_lang-Dvt6ywSf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B8nQAqQg.js";import"./createLucideIcon-Dy3o-9bT.js";const q={class:"flex items-center justify-between"},Q={class:"flex mt-2","aria-label":"Breadcrumb"},G={class:"inline-flex items-center space-x-1 md:space-x-3"},H={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},J={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},K={class:"py-12"},W={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},X={key:0,class:"mb-6 grid grid-cols-1 md:grid-cols-4 gap-4"},Y={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6"},Z={class:"text-2xl font-bold text-gray-900 dark:text-gray-100"},ee={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6"},te={class:"text-2xl font-bold text-yellow-600"},se={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6"},ae={class:"text-2xl font-bold text-green-600"},re={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6"},le={class:"text-2xl font-bold text-red-600"},de={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},oe={class:"p-6"},ne={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ie={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ue={class:"p-6 text-gray-900 dark:text-gray-100"},ge={key:0,class:"text-center py-8"},ce={key:1,class:"text-center py-8"},xe={key:2,class:"overflow-x-auto"},pe={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},me={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ye={class:"px-6 py-4 whitespace-nowrap"},ve={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},fe={class:"text-sm text-gray-500 dark:text-gray-400"},he={class:"px-6 py-4 whitespace-nowrap"},be={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},we={class:"text-sm text-gray-500 dark:text-gray-400"},_e={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100"},ke={class:"px-6 py-4 whitespace-nowrap"},Se={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Pe={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Ue={__name:"Index",setup(Le){const M=z(),v=m(()=>{var a;return(a=M.props.auth)==null?void 0:a.user}),f=[{title:"Dashboard",href:"/dashboard"},{title:"Orders",href:"/admin/orders"}],h=c(!1),b=c([]),i=c({}),o=c(""),u=c("all"),g=c("all"),P=async()=>{var a;h.value=!0;try{const t=new URLSearchParams;o.value&&t.append("search",o.value),u.value!=="all"&&t.append("status",u.value),g.value!=="all"&&t.append("payment_status",g.value);const s=await window.axios.get(`/admin/orders-list?${t.toString()}`);b.value=((a=s.data.orders)==null?void 0:a.data)||s.data.orders||[],s.data.stats&&(i.value=s.data.stats)}catch(t){console.error("Error fetching orders:",t),b.value=[]}finally{h.value=!1}},L=m(()=>b.value.filter(a=>{var x,p;const t=!o.value||a.order_number.toLowerCase().includes(o.value.toLowerCase())||((x=a.user)==null?void 0:x.name.toLowerCase().includes(o.value.toLowerCase()))||((p=a.user)==null?void 0:p.email.toLowerCase().includes(o.value.toLowerCase())),s=u.value==="all"||a.status===u.value,n=g.value==="all"||a.payment_status===g.value;return t&&s&&n}));m(()=>{var a,t;return((t=(a=v.value)==null?void 0:a.roles)==null?void 0:t.some(s=>s.name==="admin"))||!1}),m(()=>{var a,t;return((t=(a=v.value)==null?void 0:a.user_permissions)==null?void 0:t.includes("view orders"))||!1}),m(()=>{var a,t;return((t=(a=v.value)==null?void 0:a.user_permissions)==null?void 0:t.includes("manage orders"))||!1});const A=a=>({pending:"bg-yellow-100 text-yellow-800",processing:"bg-blue-100 text-blue-800",shipped:"bg-purple-100 text-purple-800",delivered:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800",refunded:"bg-gray-100 text-gray-800"})[a]||"bg-gray-100 text-gray-800",R=a=>({pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",failed:"bg-red-100 text-red-800",refunded:"bg-gray-100 text-gray-800"})[a]||"bg-gray-100 text-gray-800",w=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),T=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return F(()=>{P()}),(a,t)=>(l(),d(C,null,[_(k(j),{title:"Order Management"}),_(I,null,{header:y(()=>[e("div",q,[e("div",null,[t[4]||(t[4]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Order Management ",-1)),e("nav",Q,[e("ol",G,[(l(),d(C,null,B(f,(s,n)=>e("li",{key:n,class:"inline-flex items-center"},[n<f.length-1?(l(),E(k(V),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:y(()=>[U(r(s.title),1)]),_:2},1032,["href"])):(l(),d("span",H,r(s.title),1)),n<f.length-1?(l(),d("svg",J,t[3]||(t[3]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):D("",!0)])),64))])])])])]),default:y(()=>[e("div",K,[e("div",W,[i.value&&Object.keys(i.value).length>0?(l(),d("div",X,[e("div",Y,[t[5]||(t[5]=e("div",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Orders",-1)),e("div",Z,r(i.value.total_orders||0),1)]),e("div",ee,[t[6]||(t[6]=e("div",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Pending Orders",-1)),e("div",te,r(i.value.pending_orders||0),1)]),e("div",se,[t[7]||(t[7]=e("div",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Revenue",-1)),e("div",ae,r(w(i.value.total_revenue||0)),1)]),e("div",re,[t[8]||(t[8]=e("div",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Pending Payments",-1)),e("div",le,r(w(i.value.pending_payments||0)),1)])])):D("",!0),e("div",de,[e("div",oe,[e("div",ne,[e("div",null,[S(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>o.value=s),type:"text",placeholder:"Search orders...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[$,o.value]])]),e("div",null,[S(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>u.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},t[9]||(t[9]=[e("option",{value:"all"},"All Statuses",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"processing"},"Processing",-1),e("option",{value:"shipped"},"Shipped",-1),e("option",{value:"delivered"},"Delivered",-1),e("option",{value:"cancelled"},"Cancelled",-1),e("option",{value:"refunded"},"Refunded",-1)]),512),[[O,u.value]])]),e("div",null,[S(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>g.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},t[10]||(t[10]=[e("option",{value:"all"},"All Payment Statuses",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"paid"},"Paid",-1),e("option",{value:"failed"},"Failed",-1),e("option",{value:"refunded"},"Refunded",-1)]),512),[[O,g.value]])]),e("div",null,[e("button",{onClick:P,class:"w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Refresh ")])])])]),e("div",ie,[e("div",ue,[h.value?(l(),d("div",ge,t[11]||(t[11]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):L.value.length===0?(l(),d("div",ce,t[12]||(t[12]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"No orders found.",-1)]))):(l(),d("div",xe,[e("table",pe,[t[14]||(t[14]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Order "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Customer "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Total "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Payment "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Date "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",me,[(l(!0),d(C,null,B(L.value,s=>{var n,x,p;return l(),d("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",ye,[e("div",ve,r(s.order_number),1),e("div",fe,r(((n=s.items)==null?void 0:n.length)||0)+" item(s) ",1)]),e("td",he,[e("div",be,r(((x=s.user)==null?void 0:x.name)||"N/A"),1),e("div",we,r(((p=s.user)==null?void 0:p.email)||"N/A"),1)]),e("td",_e,r(w(s.total_amount)),1),e("td",ke,[e("span",{class:N([A(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize"])},r(s.status),3)]),e("td",Se,[e("span",{class:N([R(s.payment_status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize"])},r(s.payment_status),3)]),e("td",Ce,r(T(s.created_at)),1),e("td",Pe,[_(k(V),{href:`/admin/orders/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:y(()=>t[13]||(t[13]=[U(" View ")])),_:2},1032,["href"])])])}),128))])])]))])])])])]),_:1})],64))}};export{Ue as default};
