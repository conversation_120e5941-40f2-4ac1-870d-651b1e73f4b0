import{r as c,c as q,w as Y,d as a,j as m,e as l,i as e,t as r,z as ne,l as F,v as G,F as A,q as z,s as $,x as L,a as B,W as Z,n as de,o as ue,f as W,u as ee,m as ce,g as te,y as me,P as pe,p as se}from"./vendor-CKE3WRFf.js";import{_ as ge}from"./AppLayout.vue_vue_type_script_setup_true_lang-Dvt6ywSf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-B8nQAqQg.js";import"./createLucideIcon-Dy3o-9bT.js";const ve={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},be={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},ye={class:"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"},xe={class:"flex items-center justify-between mb-6"},fe={class:"text-sm text-gray-600"},he={key:0,class:"mb-4 p-4 bg-green-50 border border-green-200 rounded-lg"},_e={class:"text-green-800"},we={key:1,class:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg"},ke={class:"text-red-800"},Ce={key:0,class:"p-4 bg-purple-50 rounded-lg"},$e={class:"flex items-center justify-between"},Se={class:"font-medium text-purple-900"},je={class:"text-sm text-purple-700"},Pe={class:"text-right"},Be={class:"font-bold text-purple-900"},Ne={key:0,class:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full"},Ae=["min"],Le={key:1},Ve={key:0,class:"grid grid-cols-2 md:grid-cols-3 gap-2"},De=["onClick"],Me={key:1,class:"text-center py-4 text-gray-500"},Te={class:"flex items-center justify-end space-x-4 pt-4 border-t"},Oe=["disabled"],ze={key:0,class:"fas fa-spinner fa-spin mr-2"},Fe={key:1,class:"fas fa-calendar-plus mr-2"},Ue={__name:"AppointmentBookingModal",props:{isOpen:{type:Boolean,default:!1},service:{type:Object,default:null},provider:{type:Object,default:null}},emits:["close","booked"],setup(w,{emit:I}){const S=w,H=I,i=c({date:"",time_slot:{start_time:"",end_time:""},reason:"",notes:"",is_telemedicine:!1}),p=c(!1),y=c([]),j=c(""),C=c(""),v=c(""),b=q(()=>i.value.date&&i.value.time_slot.start_time&&i.value.time_slot.end_time&&i.value.reason.trim()),h=()=>{H("close"),V()},V=()=>{i.value={date:"",time_slot:{start_time:"",end_time:""},reason:"",notes:"",is_telemedicine:!1},j.value="",y.value=[],C.value="",v.value=""},D=async u=>{if(!(!u||!S.provider))try{const n=await B.get(`/get-providers/${S.provider.id}/available-slots`,{params:{date:u}});y.value=n.data.slots||[]}catch(n){console.error("Error loading available slots:",n),y.value=[]}},U=u=>{i.value.time_slot={start_time:u.start_time,end_time:u.end_time}},N=async()=>{var u,n,P,x;if(b.value){p.value=!0,C.value="",v.value="";try{const k={provider_id:S.provider.id,service_id:(u=S.service)==null?void 0:u.id,date:i.value.date,time_slot:i.value.time_slot,reason:i.value.reason,notes:i.value.notes,is_telemedicine:i.value.is_telemedicine||((n=S.service)==null?void 0:n.is_telemedicine)||!1},g=await B.post("/save-appointment",k);g.data.appointment&&(g.data.payment_required?Z.visit(`/appointments/${g.data.appointment.id}/payment`):(v.value="Appointment booked successfully!",H("booked",g.data.appointment),setTimeout(()=>{h(),Z.visit("/appointments")},2e3)))}catch(k){console.error("Error booking appointment:",k),C.value=((x=(P=k.response)==null?void 0:P.data)==null?void 0:x.message)||"Failed to book appointment. Please try again."}finally{p.value=!1}}};Y(()=>i.value.date,u=>{u&&D(u)}),Y(()=>S.service,u=>{u&&(i.value.is_telemedicine=u.is_telemedicine||!1)});const M=q(()=>new Date().toISOString().split("T")[0]),T=u=>new Date(`2000-01-01T${u}`).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0});return(u,n)=>{var P,x,k;return w.isOpen?(l(),a("div",ve,[e("div",be,[e("div",{class:"fixed inset-0 transition-opacity bg-white bg-opacity-20 backdrop-blur-sm",onClick:h}),e("div",ye,[e("div",xe,[e("div",null,[n[3]||(n[3]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Book Appointment",-1)),e("p",fe,r((P=w.service)==null?void 0:P.name)+" with Dr. "+r((k=(x=w.provider)==null?void 0:x.user)==null?void 0:k.name),1)]),e("button",{onClick:h,class:"text-gray-400 hover:text-gray-600"},n[4]||(n[4]=[e("i",{class:"fas fa-times text-xl"},null,-1)]))]),v.value?(l(),a("div",he,[e("p",_e,r(v.value),1)])):m("",!0),C.value?(l(),a("div",we,[e("p",ke,r(C.value),1)])):m("",!0),e("form",{onSubmit:ne(N,["prevent"]),class:"space-y-6"},[w.service?(l(),a("div",Ce,[e("div",$e,[e("div",null,[e("h4",Se,r(w.service.name),1),e("p",je,r(w.service.duration)+" minutes",1)]),e("div",Pe,[e("p",Be,"$"+r(w.service.price),1),w.service.is_telemedicine?(l(),a("span",Ne," Video Call ")):m("",!0)])])])):m("",!0),e("div",null,[n[5]||(n[5]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Select Date",-1)),F(e("input",{"onUpdate:modelValue":n[0]||(n[0]=g=>i.value.date=g),type:"date",min:M.value,class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",required:""},null,8,Ae),[[G,i.value.date]])]),i.value.date?(l(),a("div",Le,[n[6]||(n[6]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Available Time Slots",-1)),y.value.length>0?(l(),a("div",Ve,[(l(!0),a(A,null,z(y.value,g=>(l(),a("button",{key:`${g.start_time}-${g.end_time}`,type:"button",onClick:Q=>U(g),class:$(["p-3 text-sm border rounded-lg transition-colors",i.value.time_slot.start_time===g.start_time?"border-purple-500 bg-purple-50 text-purple-700":"border-gray-300 hover:border-purple-300 hover:bg-purple-50"])},r(T(g.start_time))+" - "+r(T(g.end_time)),11,De))),128))])):(l(),a("div",Me," No available slots for this date "))])):m("",!0),e("div",null,[n[7]||(n[7]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Reason for Visit *",-1)),F(e("textarea",{"onUpdate:modelValue":n[1]||(n[1]=g=>i.value.reason=g),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Please describe your symptoms or reason for the appointment...",required:""},null,512),[[G,i.value.reason]])]),e("div",null,[n[8]||(n[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Additional Notes (Optional)",-1)),F(e("textarea",{"onUpdate:modelValue":n[2]||(n[2]=g=>i.value.notes=g),rows:"2",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Any additional information you'd like to share..."},null,512),[[G,i.value.notes]])]),e("div",Te,[e("button",{type:"button",onClick:h,class:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"}," Cancel "),e("button",{type:"submit",disabled:!b.value||p.value,class:$(["px-6 py-2 rounded-lg font-medium transition-colors",b.value&&!p.value?"bg-purple-600 text-white hover:bg-purple-700":"bg-gray-300 text-gray-500 cursor-not-allowed"])},[p.value?(l(),a("i",ze)):(l(),a("i",Fe)),L(" "+r(p.value?"Booking...":"Book Appointment"),1)],10,Oe)])],32)])])])):m("",!0)}}},Ee={class:"py-12"},qe={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},He={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},Ge={class:"p-6 bg-white border-b border-gray-200"},Ie={class:"flex items-center justify-between"},We={class:"flex mt-4 bg-gray-100 rounded-lg p-1 max-w-md"},Qe={class:"flex items-center space-x-4"},Re={class:"relative"},Je=["placeholder"],Ke={key:0,class:"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center"},Xe={key:0},Ye={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},Ze={class:"p-4"},et={class:"grid grid-cols-3 md:grid-cols-5 lg:grid-cols-8 xl:grid-cols-10 gap-3"},tt=["onClick"],st={class:"text-lg mb-1"},ot={class:"text-xs font-medium leading-tight"},lt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},at={class:"p-6"},rt={class:"flex items-center justify-between mb-6"},it={key:0,class:"text-center py-12"},nt={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},dt={class:"aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200 relative"},ut=["src","alt"],ct={key:1,class:"h-48 w-full flex items-center justify-center bg-gray-200"},mt={key:2,class:"absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-sm"},pt={key:3,class:"absolute top-3 right-3"},gt={class:"p-4"},vt={class:"text-lg font-semibold text-gray-900 mb-2"},bt={class:"text-gray-600 text-sm mb-3 line-clamp-2"},yt={class:"flex items-center justify-between mb-4"},xt={class:"flex items-center space-x-2"},ft={class:"text-lg font-bold text-blue-600"},ht={key:0,class:"text-sm text-gray-500 line-through"},_t={key:0,class:"text-xs text-orange-600"},wt=["onClick","disabled"],kt={key:2,class:"text-center py-12"},Ct={key:1},$t={key:0,class:"mb-6"},St={key:1,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},jt={class:"p-4"},Pt={class:"grid grid-cols-3 md:grid-cols-5 lg:grid-cols-8 xl:grid-cols-10 gap-3"},Bt=["onClick"],Nt={class:"text-lg mb-1"},At={class:"text-xs font-medium leading-tight"},Lt={key:2,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Vt={class:"p-6"},Dt={class:"flex items-center justify-between mb-6"},Mt={key:0,class:"text-center py-12"},Tt={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Ot=["onClick"],zt={class:"p-6"},Ft={class:"flex items-center justify-between mb-4"},Ut={class:"flex items-center space-x-3"},Et={class:"flex-1"},qt={class:"text-lg font-semibold text-gray-900"},Ht={class:"text-sm text-purple-600 font-medium"},Gt={class:"space-y-2 mb-4"},It={key:0,class:"text-sm text-gray-600 leading-relaxed"},Wt={class:"line-clamp-3 break-words"},Qt={class:"flex items-center text-sm text-gray-500"},Rt={class:"flex items-center text-sm text-gray-500"},Jt={class:"capitalize"},Kt={class:"flex items-center text-sm text-gray-500"},Xt={key:1,class:"flex items-center text-sm text-gray-500"},Yt={key:0,class:"mb-4"},Zt={class:"flex items-center justify-between text-sm"},es={class:"font-semibold text-purple-600"},ts={key:2,class:"text-center py-12"},ss={key:3,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},os={class:"p-6"},ls={class:"border-b border-gray-200 pb-6 mb-6"},as={class:"flex items-center space-x-4"},rs={class:"flex-1"},is={class:"text-2xl font-bold text-gray-900"},ns={class:"text-purple-600 font-medium"},ds={class:"flex items-center mt-2 space-x-4 text-sm text-gray-500"},us={key:0,class:"mt-4"},cs={class:"text-gray-600 leading-relaxed break-words"},ms={key:0,class:"text-center py-12"},ps={key:1},gs={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},vs={class:"p-6"},bs={class:"flex items-center justify-between mb-3"},ys={class:"text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full"},xs={key:0,class:"text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full"},fs={class:"text-lg font-semibold text-gray-900 mb-2"},hs={class:"text-gray-600 text-sm mb-4 line-clamp-3"},_s={class:"space-y-2 mb-4"},ws={class:"flex items-center justify-between text-sm"},ks={class:"font-medium"},Cs={class:"flex items-center justify-between text-sm"},$s={class:"font-bold text-purple-600 text-lg"},Ss=["onClick","disabled"],js={key:2,class:"text-center py-12"},Ds={__name:"Shop",setup(w){const I=de(),S=q(()=>{var o;return(o=I.props.auth)==null?void 0:o.user});q(()=>{var o;return((o=S.value)==null?void 0:o.role)==="patient"});const H=[{title:"Shop",href:"/shop"}],i=c(""),p=c("all"),y=c("name"),j=c(!1),C=c(!1),v=c("products"),b=c(null),h=c(!1),V=c(!1),D=c(null),U=c([]),N=c([]),M=c([]),T=c([]),u=c(0),n=c([{id:"all",name:"All Specializations",icon:"🏥"},{id:"general-practice",name:"General Practice",icon:"👨‍⚕️"},{id:"cardiology",name:"Cardiology",icon:"❤️"},{id:"dermatology",name:"Dermatology",icon:"🧴"},{id:"psychiatry",name:"Psychiatry",icon:"🧠"},{id:"pediatrics",name:"Pediatrics",icon:"👶"},{id:"orthopedics",name:"Orthopedics",icon:"🦴"},{id:"gynecology",name:"Gynecology",icon:"👩‍⚕️"},{id:"neurology",name:"Neurology",icon:"🧠"},{id:"oncology",name:"Oncology",icon:"🎗️"},{id:"ophthalmology",name:"Ophthalmology",icon:"👁️"}]),P=async()=>{j.value=!0;try{const o=await B.get("/providers-list");U.value=o.data.data||o.data.providers||o.data||[]}catch(o){console.error("Error loading providers:",o),U.value=[]}finally{j.value=!1}},x=async()=>{var o;j.value=!0;try{const t={category:p.value!=="all"?p.value:"",search:i.value,sort:y.value},d=await B.get("/shop/products",{params:t});M.value=((o=d.data.products)==null?void 0:o.data)||d.data.products||[]}catch(t){console.error("Error loading products:",t),M.value=[]}finally{j.value=!1}},k=async()=>{try{const o=await B.get("/shop/categories");T.value=o.data.categories||o.data||[]}catch(o){console.error("Error loading categories:",o),T.value=[]}},g=async()=>{try{const o=await B.get("/shop/cart/count");u.value=o.data.cart_count||0}catch(o){console.error("Error loading cart count:",o),u.value=0}},Q=async o=>{C.value=!0;try{const t=await B.get(`/get-providers/${o}/services`);N.value=t.data.services||[]}catch(t){console.error("Error loading provider services:",t),N.value=[]}finally{C.value=!1}},R=q(()=>{let o=U.value;if(p.value!=="all"){const t=p.value.replace("-"," ");o=o.filter(d=>{var _;const f=((_=d.specialization)==null?void 0:_.toLowerCase())||"";return f.includes(t)||f.includes("general")&&t.includes("general")||f===p.value})}if(i.value.trim()){const t=i.value.toLowerCase();o=o.filter(d=>{var f,_,E,s;return((_=(f=d.user)==null?void 0:f.name)==null?void 0:_.toLowerCase().includes(t))||((E=d.specialization)==null?void 0:E.toLowerCase().includes(t))||((s=d.bio)==null?void 0:s.toLowerCase().includes(t))})}switch(y.value){case"name":default:o.sort((t,d)=>{var f,_;return(((f=t.user)==null?void 0:f.name)||"").localeCompare(((_=d.user)==null?void 0:_.name)||"")});break}return o}),oe=o=>{if(!o.weekly_availability)return"Not specified";const t=Object.keys(o.weekly_availability).filter(d=>o.weekly_availability[d]&&o.weekly_availability[d].length>0);return t.length===0?"Not available":t.length===7?"Every day":t.map(d=>d.charAt(0).toUpperCase()+d.slice(1)).join(", ")},J=o=>!o.practice_locations||o.practice_locations.length===0?"Location not specified":o.practice_locations[0],K=o=>{v.value=o,p.value="all",i.value="",b.value=null,h.value=!1,o==="services"?P():o==="products"&&(x(),k())},le=async(o,t=1)=>{try{const d=await B.post("/shop/cart/add",{product_id:o.id,quantity:t});d.data.success?(u.value=d.data.cart_count,alert("Product added to cart!")):alert(d.data.message||"Failed to add product to cart")}catch(d){console.error("Error adding to cart:",d),alert("Failed to add product to cart")}},ae=async o=>{b.value=o,h.value=!0,await Q(o.id)},re=()=>{b.value=null,h.value=!1,N.value=[]},ie=o=>{D.value=o,V.value=!0},X=o=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(o);return ue(()=>{x(),k(),g()}),(o,t)=>(l(),a(A,null,[W(ee(ce),{title:"Shop - Medroid"}),W(ge,{breadcrumbs:H},{default:te(()=>{var d,f,_,E;return[e("div",Ee,[e("div",qe,[e("div",He,[e("div",Ge,[e("div",Ie,[e("div",null,[t[10]||(t[10]=e("h1",{class:"text-2xl font-bold text-gray-900"},"Health Shop",-1)),t[11]||(t[11]=e("p",{class:"text-gray-600 mt-1"},"Browse healthcare providers and book medical services",-1)),e("div",We,[e("button",{onClick:t[0]||(t[0]=s=>K("products")),class:$(["flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors",v.value==="products"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"])}," 🛒 Products ",2),e("button",{onClick:t[1]||(t[1]=s=>K("services")),class:$(["flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors",v.value==="services"?"bg-white text-purple-600 shadow-sm":"text-gray-600 hover:text-gray-900"])}," 🩺 Services ",2)])]),e("div",Qe,[e("div",Re,[F(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>i.value=s),type:"text",placeholder:v.value==="products"?"Search products...":"Search providers...",class:"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",onInput:t[3]||(t[3]=s=>v.value==="products"?x():P())},null,40,Je),[[G,i.value]]),t[12]||(t[12]=e("svg",{class:"absolute left-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))]),v.value==="products"?(l(),me(ee(pe),{key:0,href:"/shop/cart",class:"relative inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"},{default:te(()=>[t[13]||(t[13]=e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"})],-1)),t[14]||(t[14]=L(" Cart ")),u.value>0?(l(),a("span",Ke,r(u.value),1)):m("",!0)]),_:1})):m("",!0)])])])]),v.value==="products"?(l(),a("div",Xe,[e("div",Ye,[e("div",Ze,[t[16]||(t[16]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-3"},"Browse by Category",-1)),e("div",et,[e("button",{onClick:t[4]||(t[4]=s=>{p.value="all",x()}),class:$(["p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm",p.value==="all"?"border-blue-500 bg-blue-50 text-blue-700 shadow-sm":"border-gray-200 hover:border-blue-300 text-gray-700 hover:bg-gray-50"])},t[15]||(t[15]=[e("div",{class:"text-lg mb-1"},"🏪",-1),e("div",{class:"text-xs font-medium leading-tight"},"All Products",-1)]),2),(l(!0),a(A,null,z(T.value,s=>(l(),a("button",{key:s.id,onClick:O=>{p.value=s.id,x()},class:$(["p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm",p.value===s.id?"border-blue-500 bg-blue-50 text-blue-700 shadow-sm":"border-gray-200 hover:border-blue-300 text-gray-700 hover:bg-gray-50"])},[e("div",st,r(s.icon||"📦"),1),e("div",ot,r(s.name),1)],10,tt))),128))])])]),e("div",lt,[e("div",at,[e("div",rt,[t[18]||(t[18]=e("h2",{class:"text-lg font-semibold text-gray-900"},"Products",-1)),F(e("select",{"onUpdate:modelValue":t[5]||(t[5]=s=>y.value=s),onChange:t[6]||(t[6]=s=>x()),class:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[17]||(t[17]=[e("option",{value:"name"},"Sort by Name",-1),e("option",{value:"price_low"},"Price: Low to High",-1),e("option",{value:"price_high"},"Price: High to Low",-1),e("option",{value:"newest"},"Newest First",-1)]),544),[[se,y.value]])]),j.value?(l(),a("div",it,t[19]||(t[19]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading products...",-1)]))):M.value.length>0?(l(),a("div",nt,[(l(!0),a(A,null,z(M.value,s=>(l(),a("div",{key:s.id,class:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"},[e("div",dt,[s.primary_image?(l(),a("img",{key:0,src:`/storage/${s.primary_image}`,alt:s.name,class:"h-48 w-full object-cover object-center group-hover:opacity-75"},null,8,ut)):(l(),a("div",ct,t[20]||(t[20]=[e("i",{class:"fas fa-image text-gray-400 text-4xl"},null,-1)]))),s.is_on_sale?(l(),a("div",mt,r(s.discount_percentage)+"% OFF ",1)):m("",!0),s.type==="digital"?(l(),a("div",pt,t[21]||(t[21]=[e("span",{class:"bg-purple-500 text-white text-xs px-2 py-1 rounded shadow-sm"}," Digital ",-1)]))):m("",!0)]),e("div",gt,[e("h3",vt,r(s.name),1),e("p",bt,r(s.short_description),1),e("div",yt,[e("div",xt,[e("span",ft,r(s.formatted_price),1),s.is_on_sale?(l(),a("span",ht,r(s.formatted_original_price),1)):m("",!0)]),s.type==="physical"&&s.stock_quantity<=5?(l(),a("span",_t," Only "+r(s.stock_quantity)+" left ",1)):m("",!0)]),e("button",{onClick:O=>le(s),disabled:!s.can_purchase,class:$(["w-full py-2 px-4 rounded-lg transition-colors font-medium",s.can_purchase?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed"])},[t[22]||(t[22]=e("i",{class:"fas fa-shopping-cart mr-2"},null,-1)),L(" "+r(s.can_purchase?"Add to Cart":"Out of Stock"),1)],10,wt)])]))),128))])):(l(),a("div",kt,t[23]||(t[23]=[e("i",{class:"fas fa-box-open text-4xl text-gray-400 mb-4"},null,-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No products found",-1),e("p",{class:"text-gray-500"},"Try adjusting your search or filter criteria.",-1)])))])])])):m("",!0),v.value==="services"?(l(),a("div",Ct,[h.value?(l(),a("div",$t,[e("button",{onClick:re,class:"inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"},t[24]||(t[24]=[e("i",{class:"fas fa-arrow-left mr-2"},null,-1),L(" Back to Providers ")]))])):m("",!0),h.value?m("",!0):(l(),a("div",St,[e("div",jt,[t[25]||(t[25]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-3"},"Browse by Specialization",-1)),e("div",Pt,[(l(!0),a(A,null,z(n.value,s=>(l(),a("button",{key:s.id,onClick:O=>p.value=s.id,class:$(["p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm",p.value===s.id?"border-purple-500 bg-purple-50 text-purple-700 shadow-sm":"border-gray-200 hover:border-purple-300 text-gray-700 hover:bg-gray-50"])},[e("div",Nt,r(s.icon),1),e("div",At,r(s.name),1)],10,Bt))),128))])])])),h.value?(l(),a("div",ss,[e("div",os,[e("div",ls,[e("div",as,[t[38]||(t[38]=e("div",{class:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center"},[e("i",{class:"fas fa-user-md text-purple-600 text-2xl"})],-1)),e("div",rs,[e("h2",is," Dr. "+r(((f=(d=b.value)==null?void 0:d.user)==null?void 0:f.name)||"Provider"),1),e("p",ns,r((_=b.value)==null?void 0:_.specialization),1),e("div",ds,[e("span",null,[t[37]||(t[37]=e("i",{class:"fas fa-map-marker-alt mr-1"},null,-1)),L(r(J(b.value)),1)])])])]),(E=b.value)!=null&&E.bio?(l(),a("div",us,[e("p",cs,r(b.value.bio),1)])):m("",!0)]),C.value?(l(),a("div",ms,t[39]||(t[39]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading services...",-1)]))):N.value.length>0?(l(),a("div",ps,[t[43]||(t[43]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Available Services",-1)),e("div",gs,[(l(!0),a(A,null,z(N.value,s=>(l(),a("div",{key:s.id,class:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"},[e("div",vs,[e("div",bs,[e("span",ys,r(s.category||"Service"),1),s.is_telemedicine?(l(),a("span",xs," Video Call ")):m("",!0)]),e("h4",fs,r(s.name),1),e("p",hs,r(s.description),1),e("div",_s,[e("div",ws,[t[40]||(t[40]=e("span",{class:"text-gray-500"},"Duration:",-1)),e("span",ks,r(s.duration)+" minutes",1)]),e("div",Cs,[t[41]||(t[41]=e("span",{class:"text-gray-500"},"Price:",-1)),e("span",$s,r(X(s.price)),1)])]),e("button",{onClick:O=>ie(s),disabled:!s.active,class:$(["w-full py-2 px-4 rounded-lg transition-colors font-medium",s.active?"bg-purple-600 text-white hover:bg-purple-700":"bg-gray-300 text-gray-500 cursor-not-allowed"])},[t[42]||(t[42]=e("i",{class:"fas fa-calendar-plus mr-2"},null,-1)),L(" "+r(s.active?"Book Appointment":"Unavailable"),1)],10,Ss)])]))),128))])])):(l(),a("div",js,t[44]||(t[44]=[e("i",{class:"fas fa-stethoscope text-4xl text-gray-400 mb-4"},null,-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No services available",-1),e("p",{class:"text-gray-500"},"This provider hasn't listed any services yet.",-1)])))])])):(l(),a("div",Lt,[e("div",Vt,[e("div",Dt,[t[27]||(t[27]=e("h2",{class:"text-lg font-semibold text-gray-900"},"Healthcare Providers",-1)),F(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>y.value=s),class:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"},t[26]||(t[26]=[e("option",{value:"name"},"Sort by Name",-1)]),512),[[se,y.value]])]),j.value?(l(),a("div",Mt,t[28]||(t[28]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading providers...",-1)]))):R.value.length>0?(l(),a("div",Tt,[(l(!0),a(A,null,z(R.value,s=>{var O;return l(),a("div",{key:s.id,class:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer",onClick:Ps=>ae(s)},[e("div",zt,[e("div",Ft,[e("div",Ut,[t[29]||(t[29]=e("div",{class:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center"},[e("i",{class:"fas fa-user-md text-purple-600 text-xl"})],-1)),e("div",Et,[e("h3",qt," Dr. "+r(((O=s.user)==null?void 0:O.name)||"Provider"),1),e("p",Ht,r(s.specialization||"General Practice"),1)])])]),e("div",Gt,[s.bio?(l(),a("div",It,[e("p",Wt,r(s.bio),1)])):m("",!0),e("div",Qt,[t[30]||(t[30]=e("i",{class:"fas fa-map-marker-alt mr-2 text-gray-400"},null,-1)),e("span",null,r(J(s)),1)]),e("div",Rt,[t[31]||(t[31]=e("i",{class:"fas fa-venus-mars mr-2 text-gray-400"},null,-1)),e("span",Jt,r(s.gender||"Not specified"),1)]),e("div",Kt,[t[32]||(t[32]=e("i",{class:"fas fa-calendar mr-2 text-gray-400"},null,-1)),e("span",null,r(oe(s)),1)]),s.languages&&s.languages.length>0?(l(),a("div",Xt,[t[33]||(t[33]=e("i",{class:"fas fa-language mr-2 text-gray-400"},null,-1)),e("span",null,r(s.languages.slice(0,2).join(", "))+r(s.languages.length>2?"...":""),1)])):m("",!0)]),s.pricing?(l(),a("div",Yt,[e("div",Zt,[t[34]||(t[34]=e("span",{class:"text-gray-600"},"Consultation:",-1)),e("span",es,r(X(s.pricing.consultation||0)),1)])])):m("",!0),t[35]||(t[35]=e("button",{class:"w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium"},[e("i",{class:"fas fa-eye mr-2"}),L(" View Services ")],-1))])],8,Ot)}),128))])):(l(),a("div",ts,t[36]||(t[36]=[e("i",{class:"fas fa-user-md text-4xl text-gray-400 mb-4"},null,-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No providers found",-1),e("p",{class:"text-gray-500"},"Try adjusting your search or filter criteria.",-1)])))])]))])):m("",!0)])]),W(Ue,{"is-open":V.value,service:D.value,provider:b.value,onClose:t[8]||(t[8]=s=>{V.value=!1,D.value=null}),onBooked:t[9]||(t[9]=s=>{V.value=!1,D.value=null})},null,8,["is-open","service","provider"])]}),_:1})],64))}};export{Ds as default};
